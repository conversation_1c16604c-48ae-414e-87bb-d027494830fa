
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>eCRF注释指南 - 可打印版本</title>
    <style>
        @page {
            size: A4;
            margin: 2cm;
        }
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
        }
        .header {
            text-align: center;
            border-bottom: 3px solid #333;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .header h1 {
            font-size: 24px;
            margin: 0;
            color: #2c3e50;
        }
        .section {
            margin: 25px 0;
            page-break-inside: avoid;
        }
        .section h2 {
            font-size: 16px;
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 5px;
            margin-bottom: 15px;
        }
        .domain-box {
            background-color: #BFFFFF;
            border: 3px solid #000;
            padding: 15px 20px;
            margin: 15px 0;
            display: inline-block;
            font-weight: bold;
            font-size: 18px;
            border-radius: 5px;
        }
        .variable-box {
            background-color: #BFFFFF;
            border: 2px solid #000;
            padding: 8px 12px;
            margin: 5px;
            display: inline-block;
            font-size: 12px;
            border-radius: 3px;
            font-weight: bold;
        }
        .spec-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        .spec-table th, .spec-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .spec-table th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .info-box {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            margin: 15px 0;
            border-radius: 5px;
        }
        .step-list {
            background-color: #e8f4fd;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .step-list ol {
            margin: 0;
            padding-left: 20px;
        }
        .step-list li {
            margin: 8px 0;
        }
        .page-break {
            page-break-before: always;
        }
        .no-break {
            page-break-inside: avoid;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>eCRF注释实施指南</h1>
        <p>基于SDTM规范的电子病例报告表注释标准</p>
        <p>生成时间: 2025年06月20日 18:34:30</p>
        <p>源文件: dm_spec.xlsx</p>
    </div>

    <div class="section">
        <h2>1. 基本信息</h2>
        <div class="info-box">
            <p><strong>Domain:</strong> DM</p>
            <p><strong>OBSCLASS:</strong> 人口学资料</p>
            <p><strong>Domain标签:</strong> DM (人口学资料)</p>
            <p><strong>标准颜色:</strong> BLUE (RGB: 191, 255, 255)</p>
            <p><strong>变量数量:</strong> 4</p>
        </div>
    </div>

    <div class="section">
        <h2>2. Domain标识框样式</h2>
        <p>在eCRF表单顶部添加以下样式的Domain标识框：</p>
        
        <div style="text-align: center; margin: 20px 0;">
            <div class="domain-box">DM (人口学资料)</div>
        </div>
        
        <table class="spec-table">
            <tr><th>属性</th><th>值</th><th>说明</th></tr>
            <tr><td>背景颜色</td><td>#BFFFFF</td><td>RGB: 191, 255, 255</td></tr>
            <tr><td>边框</td><td>3px 黑色实线</td><td>清晰的边界</td></tr>
            <tr><td>字体</td><td>粗体 18px</td><td>确保可读性</td></tr>
            <tr><td>内边距</td><td>15px 20px</td><td>适当的内部空间</td></tr>
            <tr><td>位置</td><td>表单顶部</td><td>显著位置</td></tr>
        </table>
    </div>

    <div class="section">
        <h2>3. 变量注释框样式</h2>
        <p>在每个数据字段旁边添加对应的变量注释框：</p>
        
        <div style="margin: 20px 0;">
            <div class="variable-box">BRTHDTC</div>
            <div class="variable-box">AGE</div>
            <div class="variable-box">AGEU</div>
            <div class="variable-box">SEX</div>

        </div>
        
        <table class="spec-table">
            <tr><th>属性</th><th>值</th><th>说明</th></tr>
            <tr><td>背景颜色</td><td>#BFFFFF</td><td>与Domain框相同</td></tr>
            <tr><td>边框</td><td>2px 黑色实线</td><td>清晰但不突兀</td></tr>
            <tr><td>字体</td><td>粗体 12px</td><td>清晰可读</td></tr>
            <tr><td>内边距</td><td>8px 12px</td><td>紧凑布局</td></tr>
            <tr><td>位置</td><td>字段旁边</td><td>就近原则</td></tr>
        </table>
    </div>

    <div class="section page-break">
        <h2>4. 实施步骤</h2>
        <div class="step-list">
            <ol>
                <li><strong>准备工作</strong>
                    <ul>
                        <li>安装PDF编辑器 (推荐Adobe Acrobat或Foxit PDF Editor)</li>
                        <li>准备原始eCRF文件: HY1005-2023-2-P1_Unique eCRF_V3.0.pdf</li>
                    </ul>
                </li>
                <li><strong>添加Domain标识</strong>
                    <ul>
                        <li>在表单顶部添加Domain标识框</li>
                        <li>文本内容: DM (人口学资料)</li>
                        <li>使用BLUE背景色 (RGB: 191, 255, 255)</li>
                        <li>设置3px黑色边框</li>
                    </ul>
                </li>
                <li><strong>添加变量注释</strong>
                    <ul>
                        <li>在每个相关数据字段旁边添加变量注释框</li>
                        <li>使用与Domain框相同的背景色</li>
                        <li>设置2px黑色边框</li>
                        <li>确保注释不遮挡原有内容</li>
                    </ul>
                </li>
                <li><strong>质量检查</strong>
                    <ul>
                        <li>检查所有注释的颜色一致性</li>
                        <li>确认注释位置合理</li>
                        <li>验证文字清晰可读</li>
                    </ul>
                </li>
                <li><strong>保存文件</strong>
                    <ul>
                        <li>另存为新的PDF文件</li>
                        <li>建议命名为: [原文件名]_annotated.pdf</li>
                    </ul>
                </li>
            </ol>
        </div>
    </div>

    <div class="section">
        <h2>5. 变量详细信息</h2>
        <table class="spec-table">
            <tr>
                <th>序号</th>
                <th>变量名 (VARNAM)</th>
                <th>变量标签 (VARLABEL)</th>
                <th>注释位置建议</th>
            </tr>

            <tr>
                <td>1</td>
                <td><strong>BRTHDTC</strong></td>
                <td>出生日期/时间</td>
                <td>出生日期/时间字段旁边</td>
            </tr>

            <tr>
                <td>2</td>
                <td><strong>AGE</strong></td>
                <td>年龄</td>
                <td>年龄字段旁边</td>
            </tr>

            <tr>
                <td>3</td>
                <td><strong>AGEU</strong></td>
                <td>年龄单位</td>
                <td>年龄单位字段旁边</td>
            </tr>

            <tr>
                <td>4</td>
                <td><strong>SEX</strong></td>
                <td>性别</td>
                <td>性别字段旁边</td>
            </tr>

        </table>
    </div>

    <div class="section">
        <h2>6. 技术规格总结</h2>
        <div class="info-box">
            <h4>Domain标识框:</h4>
            <ul>
                <li>文本: DM (人口学资料)</li>
                <li>背景: #BFFFFF (RGB: 191, 255, 255)</li>
                <li>边框: 3px solid #000000</li>
                <li>字体: bold 18px</li>
                <li>位置: 表单顶部</li>
            </ul>
            
            <h4>变量注释框:</h4>
            <ul>
                <li>背景: #BFFFFF (与Domain框相同)</li>
                <li>边框: 2px solid #000000</li>
                <li>字体: bold 12px</li>
                <li>位置: 相应字段旁边</li>
            </ul>
        </div>
    </div>

    <div class="section">
        <h2>7. 注意事项</h2>
        <div class="info-box">
            <ul>
                <li><strong>颜色一致性:</strong> 确保同一Domain的所有注释使用相同颜色</li>
                <li><strong>位置合理:</strong> 注释应放在不遮挡原有内容的位置</li>
                <li><strong>字体清晰:</strong> 使用黑色字体确保在彩色背景上清晰可读</li>
                <li><strong>边框规范:</strong> Domain框使用3px边框，变量框使用2px边框</li>
                <li><strong>标准遵循:</strong> 严格按照CDISC SDTM标准执行</li>
            </ul>
        </div>
    </div>

    <div class="section">
        <p style="text-align: center; margin-top: 40px; font-style: italic;">
            --- 本指南由eCRF注释生成器自动生成 ---<br>
            如需打印为PDF，请使用浏览器的"打印"功能，选择"另存为PDF"
        </p>
    </div>

</body>
</html>
