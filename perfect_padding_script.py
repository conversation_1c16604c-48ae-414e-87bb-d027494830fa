#!/usr/bin/env python3
"""
完美边距版PDF注释脚本
颜色框比文字大两个英文字符宽度
"""

import fitz  # PyMuPDF
import os

def calculate_text_width_with_padding(text, fontsize):
    """计算文字的实际宽度，并添加两个英文字符的边距"""
    # 根据字体大小和字符数量估算宽度
    text_width = 0
    for char in text:
        if ord(char) < 128:  # ASCII字符（英文）
            text_width += fontsize * 0.6
        else:  # 非ASCII字符（中文等）
            text_width += fontsize * 1.0
    
    # 添加两个英文字符的边距
    two_char_padding = fontsize * 0.6 * 2  # 两个英文字符的宽度
    
    return text_width + two_char_padding

def annotate_pdf_perfect_padding():
    """完美边距版注释"""
    
    pdf_path = "HY1005-2023-2-P1_Unique eCRF_V3.0.pdf"
    output_path = "HY1005-2023-2-P1_Unique eCRF_V3.0_PERFECT_PADDING_annotated.pdf"
    
    # DM域颜色
    domain_color = (0.75, 1, 1)  # RGB(191,255,255)
    
    # 严格按照Excel数据的字段映射
    field_mappings = {
        "出生日期": "BRTHDTC",
        "年龄": "AGE",
        "周岁": "AGEU",
        "性别": "SEX"
    }
    
    try:
        doc = fitz.open(pdf_path)
        print(f"开始完美边距注释...")
        
        # 只处理第7页
        target_page = 6  # 第7页，索引为6
        page = doc[target_page]
        print(f"\n只处理第 {target_page + 1} 页...")
        print(f"页面宽度: {page.rect.width}")
        
        # 安全边距
        SAFE_MARGIN = 50
        
        # 1. 添加Domain标识
        text_instances = page.search_for("人口学资料")
        for rect in text_instances:
            if rect.y0 < page.rect.height * 0.3:
                print(f"找到表单标题 '人口学资料' at {rect}")
                
                # Domain标识使用完美边距
                domain_text = "DM(人口学资料)"
                domain_fontsize = 10
                domain_width = calculate_text_width_with_padding(domain_text, domain_fontsize)
                
                domain_rect = fitz.Rect(
                    rect.x0,
                    rect.y0 - 25,
                    rect.x0 + domain_width,
                    rect.y0 - 5
                )
                
                print(f"Domain文字: '{domain_text}'")
                print(f"字体大小: {domain_fontsize}")
                print(f"颜色框宽度: {domain_width:.1f}px (包含两个英文字符边距)")
                
                try:
                    text_annot = page.add_freetext_annot(
                        domain_rect,
                        domain_text,
                        fontsize=domain_fontsize,
                        text_color=(0, 0, 0),
                        fill_color=domain_color
                    )
                    text_annot.set_border(width=2, style="solid")
                    text_annot.update()
                    
                    print(f"已添加Domain标识: {domain_text} at {domain_rect}")
                    break
                    
                except Exception as e:
                    print(f"Domain注释失败: {e}")
                    break
        
        # 2. 添加变量注释 - 每个都有完美边距
        for pdf_field_name, varnam in field_mappings.items():
            text_instances = page.search_for(pdf_field_name)
            if text_instances:
                rect = text_instances[0]
                print(f"\n找到字段 '{pdf_field_name}' at {rect}")
                
                # 计算变量名的宽度（包含两个英文字符边距）
                var_fontsize = 9
                var_width = calculate_text_width_with_padding(varnam, var_fontsize)
                
                # 计算纯文字宽度（用于对比）
                pure_text_width = 0
                for char in varnam:
                    if ord(char) < 128:
                        pure_text_width += var_fontsize * 0.6
                    else:
                        pure_text_width += var_fontsize * 1.0
                
                padding_width = var_width - pure_text_width
                
                print(f"变量名: '{varnam}'")
                print(f"纯文字宽度: {pure_text_width:.1f}px")
                print(f"边距宽度: {padding_width:.1f}px (两个英文字符)")
                print(f"颜色框总宽度: {var_width:.1f}px")
                
                if pdf_field_name == "周岁":
                    print(f"AGEU特殊处理 - 完美边距...")
                    
                    # 计算AGEU的最佳位置
                    start_x = rect.x0
                    end_x = start_x + var_width
                    
                    # 检查是否超出页面
                    max_right = page.rect.width - SAFE_MARGIN
                    if end_x > max_right:
                        # 调整起始位置
                        end_x = max_right
                        start_x = end_x - var_width
                        print(f"调整AGEU位置避免出界")
                    
                    var_rect = fitz.Rect(
                        start_x,
                        rect.y0 - 18,  # 在"周岁"上方
                        end_x,
                        rect.y0 - 3
                    )
                    
                    print(f"AGEU完美边距: 起始{start_x:.1f}, 结束{end_x:.1f}")
                    print(f"距离右边缘: {page.rect.width - end_x:.1f} 像素")
                
                else:
                    # 其他字段的完美边距
                    start_x = rect.x1 + 10
                    end_x = start_x + var_width
                    
                    # 检查边界
                    max_right = page.rect.width - SAFE_MARGIN
                    if end_x > max_right:
                        # 调整到左侧
                        end_x = rect.x0 - 10
                        start_x = end_x - var_width
                        print(f"调整{varnam}到左侧，避免超出页面")
                    
                    var_rect = fitz.Rect(
                        start_x,
                        rect.y0 - 2,
                        end_x,
                        rect.y0 + 16
                    )
                    
                    print(f"{varnam}完美边距: 起始{start_x:.1f}, 结束{end_x:.1f}")
                
                # 添加变量注释
                try:
                    text_annot = page.add_freetext_annot(
                        var_rect,
                        varnam,
                        fontsize=var_fontsize,
                        text_color=(0, 0, 0),
                        fill_color=domain_color
                    )
                    text_annot.set_border(width=1, style="solid")
                    text_annot.update()
                    
                    print(f"已添加变量注释: {varnam} for '{pdf_field_name}' at {var_rect}")
                    
                except Exception as e:
                    print(f"变量注释失败 {varnam}: {e}")
        
        # 保存完美边距版本
        doc.save(output_path)
        doc.close()
        
        print(f"\n完美边距版完成!")
        print(f"输出文件: {output_path}")
        
        return True
        
    except Exception as e:
        print(f"注释失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_padding_calculation():
    """测试边距计算"""
    print("测试完美边距计算:")
    test_texts = ["AGEU", "BRTHDTC", "AGE", "SEX"]
    
    for text in test_texts:
        # 纯文字宽度
        pure_width = 0
        for char in text:
            if ord(char) < 128:
                pure_width += 9 * 0.6
            else:
                pure_width += 9 * 1.0
        
        # 包含边距的宽度
        padded_width = calculate_text_width_with_padding(text, 9)
        padding = padded_width - pure_width
        
        print(f"'{text}': 纯文字{pure_width:.1f}px + 边距{padding:.1f}px = 总宽度{padded_width:.1f}px")

if __name__ == "__main__":
    print("完美边距版PDF注释脚本")
    print("="*60)
    
    print("完美边距特性:")
    print("1. 颜色框比文字大两个英文字符宽度")
    print("2. 文字在颜色框中有充足空间")
    print("3. 视觉效果更美观")
    print("4. 确保不超出页面边界")
    
    # 测试边距计算
    print("\n测试完美边距计算...")
    test_padding_calculation()
    
    # 检查PDF文件
    pdf_path = "HY1005-2023-2-P1_Unique eCRF_V3.0.pdf"
    if not os.path.exists(pdf_path):
        print(f"错误: PDF文件不存在 - {pdf_path}")
        exit(1)
    
    print("\n执行完美边距注释...")
    success = annotate_pdf_perfect_padding()
    
    if success:
        print("\n完美边距版完成!")
        print("边距效果:")
        print("1. 每个颜色框都比文字大两个英文字符")
        print("2. AGEU颜色框有充足的空间显示文字")
        print("3. 视觉效果更加美观和专业")
        print("4. 确保不超出页面边界")
        print("\n完美边距文件: HY1005-2023-2-P1_Unique eCRF_V3.0_PERFECT_PADDING_annotated.pdf")
    else:
        print("\n注释失败，请检查错误信息")
