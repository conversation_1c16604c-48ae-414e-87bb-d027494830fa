import pandas as pd
import json

def read_excel_data(excel_path):
    """读取Excel数据"""
    try:
        df = pd.read_excel(excel_path)
        print("成功读取Excel文件")
        print(f"数据行数: {len(df)}")
        print(f"列名: {df.columns.tolist()}")
        return df
    except Exception as e:
        print(f"读取Excel文件失败: {e}")
        return None

def prepare_annotation_data(excel_data):
    """准备注释数据"""
    if excel_data is None or excel_data.empty:
        return None
    
    # 获取基本信息
    domain = excel_data['DOMAIN'].iloc[0] if 'DOMAIN' in excel_data.columns else 'Unknown'
    obsclass = excel_data['OBSCLASS'].iloc[0] if 'OBSCLASS' in excel_data.columns else 'Unknown'
    
    # 创建注释数据结构
    annotation_data = {
        'domain_info': {
            'domain': domain,
            'obsclass': obsclass,
            'domain_label': f"{domain} ({obsclass})",
            'color_info': get_domain_color(domain)
        },
        'variables': []
    }
    
    # 添加变量信息
    for index, row in excel_data.iterrows():
        var_info = {
            'varnam': row['VARNAM'] if 'VARNAM' in row and pd.notna(row['VARNAM']) else 'N/A',
            'varlabel': row['VARLABEL'] if 'VARLABEL' in row and pd.notna(row['VARLABEL']) else 'N/A',
            'order': row['ORDER'] if 'ORDER' in row and pd.notna(row['ORDER']) else index + 1
        }
        annotation_data['variables'].append(var_info)
    
    return annotation_data

def get_domain_color(domain):
    """根据Domain获取颜色信息"""
    color_mapping = {
        'DM': {
            'name': 'BLUE',
            'rgb': '191, 255, 255',
            'description': 'Demographics'
        },
        'DS': {
            'name': 'YELLOW', 
            'rgb': '255, 255, 150',
            'description': 'Disposition'
        },
        'SC': {
            'name': 'GREEN',
            'rgb': '150, 255, 150', 
            'description': 'Subject Characteristics'
        },
        'VS': {
            'name': 'ORANGE',
            'rgb': '255, 190, 155',
            'description': 'Vital Signs'
        }
    }
    
    return color_mapping.get(domain, {
        'name': 'BLUE',
        'rgb': '191, 255, 255',
        'description': 'Unknown'
    })

def generate_annotation_instructions(annotation_data):
    """生成注释说明"""
    if not annotation_data:
        return
    
    print("\n" + "="*60)
    print("eCRF 注释说明")
    print("="*60)
    
    # Domain信息
    domain_info = annotation_data['domain_info']
    print(f"\n1. Domain标识注释:")
    print(f"   文本: {domain_info['domain_label']}")
    print(f"   颜色: {domain_info['color_info']['name']}")
    print(f"   RGB值: {domain_info['color_info']['rgb']}")
    print(f"   说明: {domain_info['color_info']['description']}")
    
    # 变量信息
    print(f"\n2. 变量注释 (共{len(annotation_data['variables'])}个变量):")
    for i, var in enumerate(annotation_data['variables'], 1):
        print(f"   {i}. {var['varnam']}: {var['varlabel']}")
    
    print(f"\n3. 注释格式说明:")
    print(f"   - 在eCRF对应的表单位置添加Domain标识框")
    print(f"   - 使用{domain_info['color_info']['name']}色背景")
    print(f"   - 在相应的变量位置添加VARNAM注释")
    print(f"   - 注释格式参考提供的截图样式")

def save_annotation_data(annotation_data, output_file):
    """保存注释数据到JSON文件"""
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(annotation_data, f, ensure_ascii=False, indent=2)
        print(f"\n注释数据已保存到: {output_file}")
    except Exception as e:
        print(f"保存注释数据失败: {e}")

def create_annotation_csv(annotation_data, output_file):
    """创建注释信息的CSV文件"""
    if not annotation_data:
        return
    
    # 创建注释信息的DataFrame
    rows = []
    
    # 添加Domain信息行
    domain_info = annotation_data['domain_info']
    rows.append({
        'Type': 'Domain',
        'Position': 'Form Header',
        'Text': domain_info['domain_label'],
        'Color': domain_info['color_info']['name'],
        'RGB': domain_info['color_info']['rgb'],
        'Description': f"Domain identifier for {domain_info['color_info']['description']}"
    })
    
    # 添加变量信息行
    for var in annotation_data['variables']:
        rows.append({
            'Type': 'Variable',
            'Position': f"Field for {var['varlabel']}",
            'Text': var['varnam'],
            'Color': domain_info['color_info']['name'],
            'RGB': domain_info['color_info']['rgb'],
            'Description': var['varlabel']
        })
    
    # 创建DataFrame并保存
    df = pd.DataFrame(rows)
    df.to_csv(output_file, index=False, encoding='utf-8-sig')
    print(f"注释信息CSV已保存到: {output_file}")

def main():
    """主函数"""
    excel_path = "dm_spec.xlsx"
    
    print("eCRF注释准备程序")
    print("="*40)
    
    # 读取Excel数据
    excel_data = read_excel_data(excel_path)
    
    if excel_data is not None:
        print("\nExcel数据预览:")
        print(excel_data[['DOMAIN', 'OBSCLASS', 'VARNAM', 'VARLABEL']].to_string())
        
        # 准备注释数据
        annotation_data = prepare_annotation_data(excel_data)
        
        if annotation_data:
            # 生成注释说明
            generate_annotation_instructions(annotation_data)
            
            # 保存注释数据
            save_annotation_data(annotation_data, "annotation_data.json")
            create_annotation_csv(annotation_data, "annotation_instructions.csv")
            
            print("\n" + "="*60)
            print("处理完成!")
            print("生成的文件:")
            print("- annotation_data.json: 注释数据(JSON格式)")
            print("- annotation_instructions.csv: 注释说明(CSV格式)")
            print("\n请根据生成的说明在eCRF PDF中添加相应的注释。")
        else:
            print("无法准备注释数据")
    else:
        print("无法读取Excel数据")

if __name__ == "__main__":
    main()
