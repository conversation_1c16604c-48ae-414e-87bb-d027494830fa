#!/usr/bin/env python3
"""
最终清理版PDF注释脚本
1. 删除第41页多余注释
2. 重新调整AGEU位置，避免遮挡原版字符
"""

import fitz  # PyMuPDF
import os

def annotate_pdf_clean_final():
    """最终清理版注释"""
    
    pdf_path = "HY1005-2023-2-P1_Unique eCRF_V3.0.pdf"
    output_path = "HY1005-2023-2-P1_Unique eCRF_V3.0_CLEAN_FINAL_annotated.pdf"
    
    # DM域颜色
    domain_color = (0.75, 1, 1)  # RGB(191,255,255)
    
    # 严格按照Excel数据的字段映射
    field_mappings = {
        "出生日期": "BRTHDTC",
        "年龄": "AGE",
        "周岁": "AGEU",
        "性别": "SEX"
    }
    
    try:
        doc = fitz.open(pdf_path)
        print(f"开始最终清理注释...")
        print(f"修正: 1.删除第41页注释 2.重新调整AGEU位置")
        
        domain_added = False
        
        # 只处理第7页，不处理第41页
        target_page = 6  # 第7页，索引为6
        
        page = doc[target_page]
        print(f"\n只处理第 {target_page + 1} 页（主要表单）...")
        
        # 1. 添加Domain标识
        text_instances = page.search_for("人口学资料")
        for rect in text_instances:
            # 检查是否在表单位置
            if rect.y0 < page.rect.height * 0.3:
                print(f"找到表单标题 '人口学资料' at {rect}")
                
                # Domain标识位置
                domain_rect = fitz.Rect(
                    rect.x0,              # 与标题左对齐
                    rect.y0 - 25,         # 在标题上方
                    rect.x0 + 120,        # 合适宽度
                    rect.y0 - 5           # 不要太高
                )
                
                # 使用自由文本注释
                try:
                    text_annot = page.add_freetext_annot(
                        domain_rect,
                        "DM(人口学资料)",
                        fontsize=10,
                        text_color=(0, 0, 0),     # 黑色文字
                        fill_color=domain_color   # 彩色背景
                    )
                    text_annot.set_border(width=2, style="solid")
                    text_annot.update()
                    
                    print(f"已添加Domain标识: DM(人口学资料) at {domain_rect}")
                    domain_added = True
                    break
                    
                except Exception as e:
                    print(f"Domain注释失败: {e}")
                    break
        
        # 2. 添加变量注释 - 特别处理AGEU
        for pdf_field_name, varnam in field_mappings.items():
            text_instances = page.search_for(pdf_field_name)
            if text_instances:
                rect = text_instances[0]
                print(f"找到字段 '{pdf_field_name}' at {rect}")
                
                # 特别处理AGEU - 详细分析周围环境
                if pdf_field_name == "周岁":
                    print(f"特别处理AGEU位置...")
                    
                    # 分析"周岁"周围的文本内容
                    # 检查右侧区域
                    right_area = fitz.Rect(rect.x1, rect.y0 - 5, rect.x1 + 100, rect.y1 + 5)
                    right_text = page.get_textbox(right_area)
                    
                    # 检查下方区域
                    below_area = fitz.Rect(rect.x0 - 10, rect.y1, rect.x1 + 50, rect.y1 + 20)
                    below_text = page.get_textbox(below_area)
                    
                    # 检查上方区域
                    above_area = fitz.Rect(rect.x0 - 10, rect.y0 - 20, rect.x1 + 50, rect.y0)
                    above_text = page.get_textbox(above_area)
                    
                    print(f"周岁右侧内容: '{right_text.strip()}'")
                    print(f"周岁下方内容: '{below_text.strip()}'")
                    print(f"周岁上方内容: '{above_text.strip()}'")
                    
                    # 智能选择位置
                    if not right_text.strip() or len(right_text.strip()) < 2:
                        # 右侧空白，放在右侧
                        var_rect = fitz.Rect(
                            rect.x1 + 8,          # 在"周岁"右侧，留点空间
                            rect.y0 - 1,
                            rect.x1 + 55,         # 较小宽度
                            rect.y0 + 14
                        )
                        print(f"AGEU放在右侧（右侧空白）")
                    elif not above_text.strip():
                        # 上方空白，放在上方
                        var_rect = fitz.Rect(
                            rect.x0,              # 与"周岁"左对齐
                            rect.y0 - 18,         # 在"周岁"上方
                            rect.x0 + 50,
                            rect.y0 - 3
                        )
                        print(f"AGEU放在上方（上方空白）")
                    else:
                        # 都有内容，放在左侧
                        var_rect = fitz.Rect(
                            rect.x0 - 55,         # 在"周岁"左侧
                            rect.y0 - 1,
                            rect.x0 - 5,
                            rect.y0 + 14
                        )
                        print(f"AGEU放在左侧（避免遮挡）")
                
                else:
                    # 其他字段的常规处理
                    var_rect = fitz.Rect(
                        rect.x1 + 10,
                        rect.y0 - 2,
                        rect.x1 + 70,
                        rect.y0 + 16
                    )
                
                # 确保注释框在页面内
                if var_rect.x1 > page.rect.width - 10:
                    var_rect = fitz.Rect(
                        rect.x0 - 70,
                        rect.y0 - 2,
                        rect.x0 - 10,
                        rect.y0 + 16
                    )
                    print(f"调整{varnam}到左侧，避免超出页面")
                
                if var_rect.x0 < 10:
                    var_rect = fitz.Rect(
                        10,
                        var_rect.y0,
                        80,
                        var_rect.y1
                    )
                    print(f"调整{varnam}到页面内")
                
                # 添加变量注释
                try:
                    text_annot = page.add_freetext_annot(
                        var_rect,
                        varnam,
                        fontsize=9,
                        text_color=(0, 0, 0),     # 黑色文字
                        fill_color=domain_color   # 彩色背景
                    )
                    text_annot.set_border(width=1, style="solid")
                    text_annot.update()
                    
                    print(f"已添加变量注释: {varnam} for '{pdf_field_name}' at {var_rect}")
                    
                except Exception as e:
                    print(f"变量注释失败 {varnam}: {e}")
        
        # 保存最终清理版本
        doc.save(output_path)
        doc.close()
        
        print(f"\n最终清理完成!")
        print(f"输出文件: {output_path}")
        
        return True
        
    except Exception as e:
        print(f"注释失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def analyze_ageu_area():
    """分析AGEU周围区域的详细内容"""
    pdf_path = "HY1005-2023-2-P1_Unique eCRF_V3.0.pdf"
    
    try:
        doc = fitz.open(pdf_path)
        page = doc[6]  # 第7页
        
        # 找到"周岁"
        text_instances = page.search_for("周岁")
        if text_instances:
            rect = text_instances[0]
            print(f"分析'周岁'周围环境:")
            print(f"'周岁'位置: {rect}")
            
            # 详细分析周围区域
            areas = {
                "右侧": fitz.Rect(rect.x1, rect.y0 - 5, rect.x1 + 100, rect.y1 + 5),
                "下方": fitz.Rect(rect.x0 - 10, rect.y1, rect.x1 + 50, rect.y1 + 20),
                "上方": fitz.Rect(rect.x0 - 10, rect.y0 - 20, rect.x1 + 50, rect.y0),
                "左侧": fitz.Rect(rect.x0 - 60, rect.y0 - 5, rect.x0, rect.y1 + 5)
            }
            
            for direction, area in areas.items():
                text_content = page.get_textbox(area)
                print(f"{direction}区域 {area}: '{text_content.strip()}'")
        
        doc.close()
        
    except Exception as e:
        print(f"分析失败: {e}")

if __name__ == "__main__":
    print("最终清理版PDF注释脚本")
    print("="*60)
    
    print("清理内容:")
    print("1. 删除第41页的多余注释")
    print("2. 重新分析AGEU位置，避免遮挡原版字符")
    print("3. 只在第7页添加注释")
    
    # 先分析AGEU周围环境
    print("\n分析AGEU周围环境...")
    analyze_ageu_area()
    
    # 检查PDF文件
    pdf_path = "HY1005-2023-2-P1_Unique eCRF_V3.0.pdf"
    if not os.path.exists(pdf_path):
        print(f"错误: PDF文件不存在 - {pdf_path}")
        exit(1)
    
    print("\n执行最终清理注释...")
    success = annotate_pdf_clean_final()
    
    if success:
        print("\n最终清理完成!")
        print("主要改进:")
        print("1. 删除了第41页的多余注释")
        print("2. 智能分析AGEU周围环境，选择最佳位置")
        print("3. 确保不遮挡任何原版字符")
        print("\n最终清理文件: HY1005-2023-2-P1_Unique eCRF_V3.0_CLEAN_FINAL_annotated.pdf")
    else:
        print("\n注释失败，请检查错误信息")
