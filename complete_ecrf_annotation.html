
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>eCRF注释完整预览</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .header { background-color: #f0f0f0; padding: 20px; border-radius: 5px; margin-bottom: 20px; }
        .domain-section { margin: 30px 0; border: 1px solid #ddd; padding: 20px; border-radius: 5px; }
        .domain-box { 
            border: 2px solid black; 
            padding: 15px; 
            margin: 10px 0; 
            display: inline-block;
            font-weight: bold;
            font-size: 16px;
            border-radius: 3px;
        }
        .variable-container { margin: 15px 0; }
        .variable-box { 
            border: 1px solid black; 
            padding: 8px 12px; 
            margin: 5px; 
            display: inline-block;
            font-size: 12px;
            border-radius: 3px;
        }
        .color-legend { 
            display: flex; 
            flex-wrap: wrap; 
            gap: 15px; 
            margin: 20px 0; 
            padding: 15px;
            background-color: #f9f9f9;
            border-radius: 5px;
        }
        .color-item { 
            display: flex; 
            align-items: center; 
            gap: 8px; 
        }
        .color-sample { 
            width: 30px; 
            height: 20px; 
            border: 1px solid black; 
            border-radius: 3px;
        }
        table { border-collapse: collapse; width: 100%; margin: 15px 0; }
        th, td { border: 1px solid #ddd; padding: 10px; text-align: left; }
        th { background-color: #f2f2f2; font-weight: bold; }
        .summary { background-color: #e8f4fd; padding: 15px; border-radius: 5px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="header">
        <h1>eCRF注释完整预览</h1>
        <p>基于SDTM规范的eCRF注释指南</p>
    </div>

    <div class="summary">
        <h2>摘要信息</h2>
        <p><strong>总Domain数:</strong> 1</p>
        <p><strong>总变量数:</strong> 4</p>
        <p><strong>包含的Domains:</strong> DM</p>
    </div>

    <div class="color-legend">
        <h3 style="width: 100%; margin: 0 0 10px 0;">Domain颜色图例</h3>

        <div class="color-item">
            <div class="color-sample" style="background-color: #BFFFFF;"></div>
            <span><strong>DM</strong> - Demographics</span>
        </div>

        <div class="color-item">
            <div class="color-sample" style="background-color: #FFFF96;"></div>
            <span><strong>DS</strong> - Disposition</span>
        </div>

        <div class="color-item">
            <div class="color-sample" style="background-color: #96FF96;"></div>
            <span><strong>SC</strong> - Subject Characteristics</span>
        </div>

        <div class="color-item">
            <div class="color-sample" style="background-color: #FFBE9B;"></div>
            <span><strong>VS</strong> - Vital Signs</span>
        </div>

        <div class="color-item">
            <div class="color-sample" style="background-color: #FF9696;"></div>
            <span><strong>AE</strong> - Adverse Events</span>
        </div>

        <div class="color-item">
            <div class="color-sample" style="background-color: #C896FF;"></div>
            <span><strong>CM</strong> - Concomitant Medications</span>
        </div>

        <div class="color-item">
            <div class="color-sample" style="background-color: #96FFFF;"></div>
            <span><strong>EX</strong> - Exposure</span>
        </div>

        <div class="color-item">
            <div class="color-sample" style="background-color: #FFC8C8;"></div>
            <span><strong>LB</strong> - Laboratory Data</span>
        </div>

    </div>

    <div class="domain-section">
        <h2>Domain: DM</h2>
        
        <h3>Domain标识框预览</h3>
        <div class="domain-box" style="background-color: #BFFFFF;">
            DM (人口学资料)
        </div>
        <p><strong>颜色:</strong> BLUE (RGB: 191, 255, 255)</p>
        
        <h3>变量注释框预览</h3>
        <div class="variable-container">
            <div class="variable-box" style="background-color: #BFFFFF;">BRTHDTC</div>
            <div class="variable-box" style="background-color: #BFFFFF;">AGE</div>
            <div class="variable-box" style="background-color: #BFFFFF;">AGEU</div>
            <div class="variable-box" style="background-color: #BFFFFF;">SEX</div>

        </div>
        
        <h3>变量详细信息</h3>
        <table>
            <tr>
                <th>变量名</th>
                <th>变量标签</th>
                <th>顺序</th>
                <th>CRF页码</th>
                <th>定义类型</th>
                <th>长度</th>
                <th>来源</th>
            </tr>

            <tr>
                <td><strong>BRTHDTC</strong></td>
                <td>出生日期/时间</td>
                <td>1</td>
                <td>11</td>
                <td>字符型</td>
                <td>200</td>
                <td>CRF</td>
            </tr>

            <tr>
                <td><strong>AGE</strong></td>
                <td>年龄</td>
                <td>2</td>
                <td>11</td>
                <td>数值型</td>
                <td>8</td>
                <td>CRF</td>
            </tr>

            <tr>
                <td><strong>AGEU</strong></td>
                <td>年龄单位</td>
                <td>3</td>
                <td>11</td>
                <td>字符型</td>
                <td>200</td>
                <td>CRF</td>
            </tr>

            <tr>
                <td><strong>SEX</strong></td>
                <td>性别</td>
                <td>4</td>
                <td>11</td>
                <td>字符型</td>
                <td>200</td>
                <td>CRF</td>
            </tr>

        </table>
    </div>

    
    <div class="summary">
        <h2>注释实施说明</h2>
        <ol>
            <li><strong>Domain标识:</strong> 在每个表单的顶部添加相应的Domain标识框，使用对应的颜色背景</li>
            <li><strong>变量注释:</strong> 在每个数据字段旁边添加对应的VARNAM注释框</li>
            <li><strong>颜色一致性:</strong> 同一Domain的所有注释使用相同的背景颜色</li>
            <li><strong>字体要求:</strong> 注释文字使用黑色，确保清晰可读</li>
            <li><strong>边框样式:</strong> Domain框使用2px黑色边框，变量框使用1px黑色边框</li>
        </ol>
    </div>
    
</body>
</html>
