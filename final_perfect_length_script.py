#!/usr/bin/env python3
"""
最终完美长度版PDF注释脚本
修正颜色框长度，确保不出界
"""

import fitz  # PyMuPDF
import os

def annotate_pdf_perfect_length():
    """最终完美长度版注释 - 调整颜色框长度"""
    
    pdf_path = "HY1005-2023-2-P1_Unique eCRF_V3.0.pdf"
    output_path = "HY1005-2023-2-P1_Unique eCRF_V3.0_PERFECT_LENGTH_annotated.pdf"
    
    # DM域颜色
    domain_color = (0.75, 1, 1)  # RGB(191,255,255)
    
    # 严格按照Excel数据的字段映射
    field_mappings = {
        "出生日期": "BRTHDTC",
        "年龄": "AGE",
        "周岁": "AGEU",
        "性别": "SEX"
    }
    
    try:
        doc = fitz.open(pdf_path)
        print(f"开始最终完美长度注释...")
        
        # 只处理第7页
        target_page = 6  # 第7页，索引为6
        page = doc[target_page]
        print(f"\n只处理第 {target_page + 1} 页...")
        print(f"页面宽度: {page.rect.width}")
        
        # 1. 添加Domain标识
        text_instances = page.search_for("人口学资料")
        for rect in text_instances:
            if rect.y0 < page.rect.height * 0.3:
                print(f"找到表单标题 '人口学资料' at {rect}")
                
                domain_rect = fitz.Rect(
                    rect.x0,
                    rect.y0 - 25,
                    rect.x0 + 120,
                    rect.y0 - 5
                )
                
                try:
                    text_annot = page.add_freetext_annot(
                        domain_rect,
                        "DM(人口学资料)",
                        fontsize=10,
                        text_color=(0, 0, 0),
                        fill_color=domain_color
                    )
                    text_annot.set_border(width=2, style="solid")
                    text_annot.update()
                    
                    print(f"已添加Domain标识: DM(人口学资料) at {domain_rect}")
                    break
                    
                except Exception as e:
                    print(f"Domain注释失败: {e}")
                    break
        
        # 2. 添加变量注释 - 特别注意颜色框长度
        for pdf_field_name, varnam in field_mappings.items():
            text_instances = page.search_for(pdf_field_name)
            if text_instances:
                rect = text_instances[0]
                print(f"找到字段 '{pdf_field_name}' at {rect}")
                
                if pdf_field_name == "周岁":
                    print(f"AGEU处理 - 特别注意颜色框长度...")
                    print(f"'周岁'位置: {rect}")
                    
                    # 计算AGEU注释框 - 根据文字长度调整
                    text_length = len(varnam)  # "AGEU" = 4个字符
                    estimated_width = text_length * 8 + 10  # 每个字符约8像素，加10像素边距
                    
                    print(f"AGEU文字长度: {text_length} 字符")
                    print(f"估算宽度: {estimated_width} 像素")
                    
                    # 上方位置，但调整宽度
                    var_rect = fitz.Rect(
                        rect.x0,                    # 与"周岁"左对齐
                        rect.y0 - 18,               # 在"周岁"上方
                        rect.x0 + estimated_width,  # 根据文字长度调整宽度
                        rect.y0 - 3                 # 适当高度
                    )
                    
                    # 检查是否超出页面右边界
                    if var_rect.x1 > page.rect.width - 10:
                        # 调整宽度，确保不超出页面
                        max_width = page.rect.width - 10 - rect.x0
                        var_rect = fitz.Rect(
                            rect.x0,
                            rect.y0 - 18,
                            rect.x0 + max_width,
                            rect.y0 - 3
                        )
                        print(f"调整AGEU宽度，避免超出页面: 最大宽度={max_width}")
                    
                    # 如果右边界还是有问题，向左移动起始位置
                    if var_rect.x1 > page.rect.width - 10:
                        var_rect = fitz.Rect(
                            page.rect.width - 10 - estimated_width,  # 从右边界往左计算
                            rect.y0 - 18,
                            page.rect.width - 10,                    # 右边界留10像素
                            rect.y0 - 3
                        )
                        print(f"从右边界往左调整AGEU位置")
                    
                    print(f"AGEU最终位置: {var_rect}")
                    print(f"颜色框宽度: {var_rect.x1 - var_rect.x0}")
                    print(f"是否超出页面: {var_rect.x1 > page.rect.width}")
                
                else:
                    # 其他字段的处理 - 也要检查长度
                    text_length = len(varnam)
                    estimated_width = text_length * 8 + 10
                    
                    var_rect = fitz.Rect(
                        rect.x1 + 10,
                        rect.y0 - 2,
                        rect.x1 + 10 + estimated_width,
                        rect.y0 + 16
                    )
                    
                    # 边界检查
                    if var_rect.x1 > page.rect.width - 10:
                        # 调整到左侧
                        var_rect = fitz.Rect(
                            rect.x0 - 10 - estimated_width,
                            rect.y0 - 2,
                            rect.x0 - 10,
                            rect.y0 + 16
                        )
                        print(f"调整{varnam}到左侧，避免超出页面")
                    
                    print(f"{varnam}位置: {var_rect}, 宽度: {var_rect.x1 - var_rect.x0}")
                
                # 添加变量注释
                try:
                    text_annot = page.add_freetext_annot(
                        var_rect,
                        varnam,
                        fontsize=9,
                        text_color=(0, 0, 0),
                        fill_color=domain_color
                    )
                    text_annot.set_border(width=1, style="solid")
                    text_annot.update()
                    
                    print(f"已添加变量注释: {varnam} for '{pdf_field_name}' at {var_rect}")
                    
                except Exception as e:
                    print(f"变量注释失败 {varnam}: {e}")
        
        # 保存最终完美长度版本
        doc.save(output_path)
        doc.close()
        
        print(f"\n最终完美长度版完成!")
        print(f"输出文件: {output_path}")
        
        return True
        
    except Exception as e:
        print(f"注释失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("最终完美长度版PDF注释脚本")
    print("="*60)
    
    print("长度优化:")
    print("1. 根据文字长度计算颜色框宽度")
    print("2. 确保颜色框不超出页面边界")
    print("3. 保持AGEU在'周岁'上方的正确位置")
    print("4. 字体位置自动跟随颜色框调整")
    
    # 检查PDF文件
    pdf_path = "HY1005-2023-2-P1_Unique eCRF_V3.0.pdf"
    if not os.path.exists(pdf_path):
        print(f"错误: PDF文件不存在 - {pdf_path}")
        exit(1)
    
    print("\n执行最终完美长度注释...")
    success = annotate_pdf_perfect_length()
    
    if success:
        print("\n最终完美长度版完成!")
        print("长度优化结果:")
        print("1. 颜色框长度根据文字自动调整")
        print("2. 确保不超出页面边界")
        print("3. AGEU位置保持在'周岁'上方")
        print("4. 字体在颜色框内完美显示")
        print("\n最终完美文件: HY1005-2023-2-P1_Unique eCRF_V3.0_PERFECT_LENGTH_annotated.pdf")
    else:
        print("\n注释失败，请检查错误信息")
