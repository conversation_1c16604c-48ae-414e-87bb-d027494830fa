#!/usr/bin/env python3
"""
完全修正版PDF注释脚本
严格按照Excel数据，修正所有问题
"""

import fitz  # PyMuPDF
import os

def annotate_pdf_correctly():
    """完全修正版注释 - 严格按照Excel数据"""
    
    pdf_path = "HY1005-2023-2-P1_Unique eCRF_V3.0.pdf"
    output_path = "HY1005-2023-2-P1_Unique eCRF_V3.0_CORRECT_annotated.pdf"
    
    # DM域颜色
    domain_color = (0.75, 1, 1)  # RGB(191,255,255)
    
    # 严格按照Excel数据的字段映射
    field_mappings = {
        "出生日期": "BRTHDTC",    # Excel: 出生日期/时间 -> PDF: 出生日期
        "年龄": "AGE",
        "周岁": "AGEU",           # Excel: 年龄单位 -> PDF: 周岁
        "性别": "SEX"
    }
    
    try:
        doc = fitz.open(pdf_path)
        print(f"开始修正注释...")
        print(f"严格按照Excel数据: BRTHDTC, AGE, AGEU, SEX")
        
        domain_added = False
        
        # 遍历所有页面
        for page_num in range(len(doc)):
            page = doc[page_num]
            print(f"\n处理第 {page_num + 1} 页...")
            
            # 1. 查找表单内的表名称并添加Domain标识
            if not domain_added:
                if page_num >= 6:  # 从第7页开始查找表单
                    text_instances = page.search_for("人口学资料")
                    for rect in text_instances:
                        # 检查是否在表单位置（不是目录）
                        if rect.y0 < page.rect.height * 0.3:  # 页面上30%区域
                            print(f"找到表单标题 '人口学资料' at {rect}")
                            
                            # 在表标题左侧添加Domain标识（避免遮挡）
                            domain_rect = fitz.Rect(
                                rect.x0 - 130,  # 在左侧
                                rect.y0 - 5,
                                rect.x0 - 10,   # 不遮挡原文字
                                rect.y0 + 15
                            )
                            
                            # 添加彩色背景
                            annot = page.add_rect_annot(domain_rect)
                            annot.set_colors(stroke=(0, 0, 0), fill=domain_color)
                            annot.set_border(width=2)
                            annot.set_opacity(1.0)
                            annot.update()
                            
                            # 添加黑色文字 - 修正括号内容
                            text_point = fitz.Point(domain_rect.x0 + 5, domain_rect.y0 + 12)
                            page.insert_text(text_point, "DM(人口学资料)", 
                                           fontsize=10, color=(0, 0, 0), fontname="helv")
                            
                            print(f"已添加Domain标识: DM(人口学资料) at {domain_rect}")
                            domain_added = True
                            break
            
            # 2. 只标注Excel中存在的4个字段
            for pdf_field_name, varnam in field_mappings.items():
                text_instances = page.search_for(pdf_field_name)
                if text_instances:
                    rect = text_instances[0]
                    print(f"找到字段 '{pdf_field_name}' at {rect}")
                    
                    # 计算注释框位置 - 避免遮挡原文字
                    if pdf_field_name == "周岁":
                        # 年龄单位在"周岁"右侧
                        var_rect = fitz.Rect(
                            rect.x1 + 5,      # 紧贴右侧
                            rect.y0 - 2,
                            rect.x1 + 50,     # 较小宽度
                            rect.y0 + 15
                        )
                    else:
                        # 其他字段在右侧，确保不遮挡
                        var_rect = fitz.Rect(
                            rect.x1 + 10,     # 留足空间
                            rect.y0 - 2,
                            rect.x1 + 70,     # 适当宽度
                            rect.y0 + 16
                        )
                    
                    # 检查是否会遮挡其他文字 - 如果会遮挡，调整位置
                    # 简单检查：如果注释框超出页面，调整位置
                    if var_rect.x1 > page.rect.width - 20:
                        # 调整到左侧
                        var_rect = fitz.Rect(
                            rect.x0 - 70,
                            rect.y0 - 2,
                            rect.x0 - 10,
                            rect.y0 + 16
                        )
                    
                    # 添加彩色背景
                    annot = page.add_rect_annot(var_rect)
                    annot.set_colors(stroke=(0, 0, 0), fill=domain_color)
                    annot.set_border(width=1)
                    annot.set_opacity(1.0)
                    annot.update()
                    
                    # 添加黑色文字
                    text_point = fitz.Point(var_rect.x0 + 3, var_rect.y0 + 11)
                    page.insert_text(text_point, varnam, 
                                   fontsize=9, color=(0, 0, 0), fontname="helv")
                    
                    print(f"已添加变量注释: {varnam} for '{pdf_field_name}' at {var_rect}")
        
        # 保存修正版本
        doc.save(output_path)
        doc.close()
        
        print(f"\n完全修正完成!")
        print(f"输出文件: {output_path}")
        
        return True
        
    except Exception as e:
        print(f"注释失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_domain_text():
    """测试Domain文字组合"""
    print("测试Domain标识文字:")
    domain = "DM"
    obsclass = "人口学资料"
    domain_label = f"{domain}({obsclass})"
    print(f"Domain标识: '{domain_label}'")
    print(f"长度: {len(domain_label)} 字符")

if __name__ == "__main__":
    print("完全修正版PDF注释脚本")
    print("="*60)
    
    # 测试Domain文字
    test_domain_text()
    
    # 检查PDF文件
    pdf_path = "HY1005-2023-2-P1_Unique eCRF_V3.0.pdf"
    if not os.path.exists(pdf_path):
        print(f"错误: PDF文件不存在 - {pdf_path}")
        exit(1)
    
    print("\n执行完全修正注释...")
    success = annotate_pdf_correctly()
    
    if success:
        print("\n修正完成!")
        print("主要修正:")
        print("1. 严格按照Excel数据，只标注4个变量: BRTHDTC, AGE, AGEU, SEX")
        print("2. 修正Domain标识: DM(人口学资料) - 括号内容正确")
        print("3. 调整注释位置，避免遮挡原有文字")
        print("4. Domain标识放在表标题左侧，不遮挡")
        print("\n最终文件: HY1005-2023-2-P1_Unique eCRF_V3.0_CORRECT_annotated.pdf")
    else:
        print("\n注释失败，请检查错误信息")
