import pandas as pd
import os
import glob
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

def merge_excel_files(directory_path, output_file='merged_results.xlsx'):
    """
    合并指定目录下的所有xlsx文件
    
    Args:
        directory_path: 目标目录路径
        output_file: 输出文件名
    """
    
    # 检查目录是否存在
    if not os.path.exists(directory_path):
        print(f"错误：目录 {directory_path} 不存在")
        return
    
    # 获取所有xlsx文件
    xlsx_pattern = os.path.join(directory_path, "*.xlsx")
    xlsx_files = glob.glob(xlsx_pattern)
    
    if not xlsx_files:
        print(f"在目录 {directory_path} 中没有找到xlsx文件")
        return
    
    print(f"找到 {len(xlsx_files)} 个xlsx文件")
    
    # 存储所有数据的列表
    all_data = []
    
    for file_path in xlsx_files:
        try:
            # 获取文件名（不包含路径和扩展名）
            file_name = Path(file_path).stem
            print(f"正在处理: {file_name}")
            
            # 读取Excel文件
            excel_file = pd.ExcelFile(file_path)
            
            # 获取所有sheet名称
            sheet_names = excel_file.sheet_names
            
            for sheet_name in sheet_names:
                try:
                    # 读取sheet数据
                    df = pd.read_excel(file_path, sheet_name=sheet_name)
                    
                    # 如果DataFrame为空或只有标题行，创建一个包含状态信息的行
                    if df.empty or len(df) == 0:
                        # 创建一个表示"无问题"状态的行
                        status_row = {
                            'file_name': file_name,
                            'sheet_name': sheet_name,
                            'status': '✓ Congratulations! 日志通过检查!',
                            'line': '',
                            'level': '',
                            'rx': ''
                        }
                        all_data.append(pd.DataFrame([status_row]))
                    else:
                        # 添加文件名和sheet名称列
                        df['file_name'] = file_name
                        df['sheet_name'] = sheet_name
                        
                        # 检查是否包含问题数据的标准列
                        expected_columns = ['line', 'level', 'rx']
                        missing_columns = [col for col in expected_columns if col not in df.columns]
                        
                        # 如果缺少标准列，添加空列
                        for col in missing_columns:
                            df[col] = ''
                        
                        # 如果没有line, level, rx列但有其他内容，可能是状态信息
                        if all(col not in df.columns for col in expected_columns):
                            # 检查是否包含状态信息
                            if any('Congratulations' in str(cell) or '检测到' in str(cell) 
                                  for row in df.values for cell in row):
                                # 重新组织数据为状态格式
                                status_info = ' '.join([str(cell) for row in df.values for cell in row if pd.notna(cell)])
                                status_row = {
                                    'file_name': file_name,
                                    'sheet_name': sheet_name,
                                    'status': status_info,
                                    'line': '',
                                    'level': '',
                                    'rx': ''
                                }
                                all_data.append(pd.DataFrame([status_row]))
                            else:
                                # 添加缺失的列
                                for col in expected_columns:
                                    df[col] = ''
                                df['status'] = ''
                                all_data.append(df)
                        else:
                            # 添加状态列
                            df['status'] = ''
                            all_data.append(df)
                            
                except Exception as e:
                    print(f"读取sheet {sheet_name} 时出错: {str(e)}")
                    # 创建错误信息行
                    error_row = {
                        'file_name': file_name,
                        'sheet_name': sheet_name,
                        'status': f'读取错误: {str(e)}',
                        'line': '',
                        'level': '',
                        'rx': ''
                    }
                    all_data.append(pd.DataFrame([error_row]))
                    
        except Exception as e:
            print(f"处理文件 {file_path} 时出错: {str(e)}")
            continue
    
    if not all_data:
        print("没有成功读取任何数据")
        return
    
    # 合并所有数据
    try:
        merged_df = pd.concat(all_data, ignore_index=True, sort=False)
        
        # 重新排列列的顺序
        column_order = ['file_name', 'sheet_name', 'status', 'line', 'level', 'rx']
        
        # 确保所有列都存在
        for col in column_order:
            if col not in merged_df.columns:
                merged_df[col] = ''
        
        # 添加其他可能存在的列
        other_columns = [col for col in merged_df.columns if col not in column_order]
        final_columns = column_order + other_columns
        
        merged_df = merged_df[final_columns]
        
        # 保存到Excel文件
        output_path = os.path.join(os.getcwd(), output_file)
        merged_df.to_excel(output_path, index=False, engine='openpyxl')
        
        print(f"\n合并完成！")
        print(f"总共处理了 {len(xlsx_files)} 个文件")
        print(f"合并后共有 {len(merged_df)} 行数据")
        print(f"结果已保存到: {output_path}")
        
        # 显示前几行数据预览
        print("\n数据预览:")
        print(merged_df.head())
        
    except Exception as e:
        print(f"合并数据时出错: {str(e)}")

def main():
    # 指定的目录路径
    target_directory = r"E:\SAS\sas_check\20250619\彭星-LY-M001-GD-101-m5\LY-M001-GD-101-m5\datasets\LY-M001-GD-101\analysis\legacy\programs\adam"
    
    # 输出文件名
    output_filename = "merged_excel_results.xlsx"
    
    print("开始合并Excel文件...")
    print(f"目标目录: {target_directory}")
    
    # 执行合并
    merge_excel_files(target_directory, output_filename)

if __name__ == "__main__":
    main()
