
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>eCRF注释实施指南</title>
    <style>
        body { 
            font-family: 'Microsoft YaHei', Arial, sans-serif; 
            margin: 20px; 
            line-height: 1.6; 
            background-color: #f5f5f5;
        }
        .container { 
            max-width: 1200px; 
            margin: 0 auto; 
            background-color: white; 
            padding: 30px; 
            border-radius: 10px; 
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        .header { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white; 
            padding: 30px; 
            border-radius: 10px; 
            margin-bottom: 30px; 
            text-align: center;
        }
        .section { 
            margin: 30px 0; 
            padding: 20px; 
            border: 1px solid #ddd; 
            border-radius: 8px; 
            background-color: #fafafa;
        }
        .domain-box { 
            background-color: #BFFFFF; 
            border: 3px solid #000; 
            padding: 15px 20px; 
            margin: 15px 0; 
            display: inline-block;
            font-weight: bold;
            font-size: 18px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }
        .variable-box { 
            background-color: #BFFFFF; 
            border: 2px solid #000; 
            padding: 10px 15px; 
            margin: 8px; 
            display: inline-block;
            font-size: 14px;
            border-radius: 4px;
            font-weight: bold;
            box-shadow: 0 1px 3px rgba(0,0,0,0.2);
        }
        .color-info { 
            background-color: #e8f4fd; 
            padding: 15px; 
            border-radius: 5px; 
            margin: 15px 0;
            border-left: 5px solid #2196F3;
        }
        .step-list { 
            background-color: #f0f8ff; 
            padding: 20px; 
            border-radius: 5px; 
            margin: 15px 0;
        }
        .step-list ol { 
            margin: 0; 
            padding-left: 20px;
        }
        .step-list li { 
            margin: 10px 0; 
            font-size: 16px;
        }
        table { 
            border-collapse: collapse; 
            width: 100%; 
            margin: 20px 0; 
            background-color: white;
        }
        th, td { 
            border: 1px solid #ddd; 
            padding: 12px; 
            text-align: left; 
        }
        th { 
            background-color: #f2f2f2; 
            font-weight: bold; 
            color: #333;
        }
        .highlight { 
            background-color: #fff3cd; 
            padding: 15px; 
            border-radius: 5px; 
            border-left: 5px solid #ffc107; 
            margin: 15px 0;
        }
        .print-section { 
            page-break-before: always; 
        }
        @media print {
            body { background-color: white; }
            .container { box-shadow: none; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📋 eCRF注释实施指南</h1>
            <p>基于SDTM规范的电子病例报告表注释标准</p>
            <p>生成时间: 2025年06月20日 18:33:11</p>
        </div>

        <div class="section">
            <h2>📊 基本信息</h2>
            <div class="color-info">
                <p><strong>源文件:</strong> dm_spec.xlsx</p>
                <p><strong>Domain:</strong> DM</p>
                <p><strong>OBSCLASS:</strong> 人口学资料</p>
                <p><strong>变量数量:</strong> 4</p>
                <p><strong>标准颜色:</strong> BLUE (RGB: 191, 255, 255)</p>
            </div>
        </div>

        <div class="section">
            <h2>🎨 Domain标识框样式</h2>
            <p>在eCRF表单顶部或相关区域添加以下样式的Domain标识框：</p>
            
            <div style="text-align: center; margin: 20px 0;">
                <div class="domain-box">DM (人口学资料)</div>
            </div>
            
            <div class="color-info">
                <h4>技术规格:</h4>
                <ul>
                    <li><strong>背景颜色:</strong> BLUE (RGB: 191, 255, 255, HEX: #BFFFFF)</li>
                    <li><strong>边框:</strong> 3px 黑色实线</li>
                    <li><strong>字体:</strong> 粗体, 18px</li>
                    <li><strong>内边距:</strong> 15px 20px</li>
                    <li><strong>圆角:</strong> 5px</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2>🏷️ 变量注释框样式</h2>
            <p>在每个数据字段旁边添加对应的变量注释框：</p>
            
            <div style="margin: 20px 0;">
                <div class="variable-box">BRTHDTC</div>
                <div class="variable-box">AGE</div>
                <div class="variable-box">AGEU</div>
                <div class="variable-box">SEX</div>

            </div>
            
            <div class="color-info">
                <h4>技术规格:</h4>
                <ul>
                    <li><strong>背景颜色:</strong> 与Domain框相同 (#BFFFFF)</li>
                    <li><strong>边框:</strong> 2px 黑色实线</li>
                    <li><strong>字体:</strong> 粗体, 14px</li>
                    <li><strong>内边距:</strong> 10px 15px</li>
                    <li><strong>圆角:</strong> 4px</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2>📋 实施步骤</h2>
            <div class="step-list">
                <ol>
                    <li><strong>打开PDF编辑器</strong> (如Adobe Acrobat, Foxit PDF Editor等)</li>
                    <li><strong>加载原始eCRF文件</strong>: HY1005-2023-2-P1_Unique eCRF_V3.0.pdf</li>
                    <li><strong>添加Domain标识框</strong>: 在表单顶部添加"DM (人口学资料)"标识</li>
                    <li><strong>设置Domain框样式</strong>: 使用BLUE背景色 (RGB: 191, 255, 255)</li>
                    <li><strong>添加变量注释</strong>: 在每个相关字段旁边添加对应的VARNAM注释</li>
                    <li><strong>保持一致性</strong>: 确保所有注释使用相同的颜色和样式</li>
                    <li><strong>检查布局</strong>: 确保注释不遮挡原有内容</li>
                    <li><strong>保存文件</strong>: 另存为带注释的eCRF版本</li>
                </ol>
            </div>
        </div>

        <div class="section">
            <h2>📝 变量详细信息</h2>
            <table>
                <tr>
                    <th>序号</th>
                    <th>变量名 (VARNAM)</th>
                    <th>变量标签 (VARLABEL)</th>
                    <th>顺序</th>
                    <th>CRF页码</th>
                    <th>定义类型</th>
                    <th>来源</th>
                </tr>

                <tr>
                    <td>1</td>
                    <td><strong>BRTHDTC</strong></td>
                    <td>出生日期/时间</td>
                    <td>1</td>
                    <td>11</td>
                    <td>字符型</td>
                    <td>CRF</td>
                </tr>

                <tr>
                    <td>2</td>
                    <td><strong>AGE</strong></td>
                    <td>年龄</td>
                    <td>2</td>
                    <td>11</td>
                    <td>数值型</td>
                    <td>CRF</td>
                </tr>

                <tr>
                    <td>3</td>
                    <td><strong>AGEU</strong></td>
                    <td>年龄单位</td>
                    <td>3</td>
                    <td>11</td>
                    <td>字符型</td>
                    <td>CRF</td>
                </tr>

                <tr>
                    <td>4</td>
                    <td><strong>SEX</strong></td>
                    <td>性别</td>
                    <td>4</td>
                    <td>11</td>
                    <td>字符型</td>
                    <td>CRF</td>
                </tr>

            </table>
        </div>

        <div class="section">
            <h2>⚠️ 重要提示</h2>
            <div class="highlight">
                <h4>注意事项:</h4>
                <ul>
                    <li>确保注释颜色与CDISC标准一致</li>
                    <li>注释框不应遮挡原有的表单内容</li>
                    <li>保持同一Domain内所有注释的视觉一致性</li>
                    <li>变量注释应放置在相应数据字段的明显位置</li>
                    <li>建议在注释完成后进行全面检查</li>
                </ul>
            </div>
        </div>

        <div class="section print-section">
            <h2>🎨 颜色标准参考</h2>
            <table>
                <tr><th>Domain</th><th>颜色名称</th><th>RGB值</th><th>十六进制</th><th>说明</th></tr>

                <tr>
                    <td><strong>DM</strong></td>
                    <td>BLUE</td>
                    <td>191, 255, 255</td>
                    <td>#BFFFFF</td>
                    <td>Demographics</td>
                </tr>

                <tr>
                    <td><strong>DS</strong></td>
                    <td>YELLOW</td>
                    <td>255, 255, 150</td>
                    <td>#FFFF96</td>
                    <td>Disposition</td>
                </tr>

                <tr>
                    <td><strong>SC</strong></td>
                    <td>GREEN</td>
                    <td>150, 255, 150</td>
                    <td>#96FF96</td>
                    <td>Subject Characteristics</td>
                </tr>

                <tr>
                    <td><strong>VS</strong></td>
                    <td>ORANGE</td>
                    <td>255, 190, 155</td>
                    <td>#FFBE9B</td>
                    <td>Vital Signs</td>
                </tr>

                <tr>
                    <td><strong>AE</strong></td>
                    <td>RED</td>
                    <td>255, 150, 150</td>
                    <td>#FF9696</td>
                    <td>Adverse Events</td>
                </tr>

                <tr>
                    <td><strong>CM</strong></td>
                    <td>PURPLE</td>
                    <td>200, 150, 255</td>
                    <td>#C896FF</td>
                    <td>Concomitant Medications</td>
                </tr>

            </table>
        </div>

        <div class="section">
            <h2>📞 技术支持</h2>
            <p>如果在实施过程中遇到问题，请参考以下资源：</p>
            <ul>
                <li>CDISC SDTM Implementation Guide</li>
                <li>FDA Study Data Technical Conformance Guide</li>
                <li>本指南生成的配套文件 (JSON, CSV格式)</li>
            </ul>
        </div>
    </div>
</body>
</html>
