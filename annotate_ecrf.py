import pandas as pd
import sys
import os
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import A4
from reportlab.lib.colors import blue, yellow, green, orange
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont

def read_excel_data(excel_path):
    """读取Excel数据"""
    try:
        df = pd.read_excel(excel_path)
        print("成功读取Excel文件")
        print(f"数据行数: {len(df)}")
        print(f"列名: {df.columns.tolist()}")
        return df
    except Exception as e:
        print(f"读取Excel文件失败: {e}")
        return None

def create_annotated_pdf(excel_data, output_path):
    """创建带注释的PDF文件"""
    
    # 创建PDF画布
    c = canvas.Canvas(output_path, pagesize=A4)
    width, height = A4
    
    # 设置字体（使用系统默认字体）
    c.setFont("Helvetica", 12)
    
    # 页面标题
    c.drawString(50, height - 50, "eCRF Annotation Based on SDTM Specifications")
    
    y_position = height - 100
    
    # 根据Excel数据创建注释
    if excel_data is not None and not excel_data.empty:
        # 获取Domain和OBSCLASS信息
        domain = excel_data['DOMAIN'].iloc[0] if 'DOMAIN' in excel_data.columns else 'Unknown'
        obsclass = excel_data['OBSCLASS'].iloc[0] if 'OBSCLASS' in excel_data.columns else 'Unknown'
        
        # 创建Domain注释框
        domain_text = f"{domain} ({obsclass})"
        
        # 根据Domain设置颜色
        if domain == 'DM':
            color = blue
            rgb_values = "191, 255, 255"
        elif domain == 'DS':
            color = yellow
            rgb_values = "255, 255, 150"
        elif domain == 'SC':
            color = green
            rgb_values = "150, 255, 150"
        elif domain == 'VS':
            color = orange
            rgb_values = "255, 190, 155"
        else:
            color = blue
            rgb_values = "191, 255, 255"
        
        # 绘制Domain标题框
        c.setFillColor(color)
        c.rect(50, y_position - 20, 300, 25, fill=1, stroke=1)
        c.setFillColor('black')
        c.drawString(60, y_position - 15, domain_text)
        c.drawString(400, y_position - 15, rgb_values)
        
        y_position -= 60
        
        # 添加变量注释
        c.drawString(50, y_position, "Variables and Labels:")
        y_position -= 30
        
        for index, row in excel_data.iterrows():
            if y_position < 100:  # 如果空间不够，创建新页面
                c.showPage()
                c.setFont("Helvetica", 12)
                y_position = height - 50
            
            varnam = row['VARNAM'] if 'VARNAM' in row and pd.notna(row['VARNAM']) else 'N/A'
            varlabel = row['VARLABEL'] if 'VARLABEL' in row and pd.notna(row['VARLABEL']) else 'N/A'
            
            # 绘制变量注释
            c.setFillColor(color)
            c.rect(70, y_position - 15, 200, 20, fill=1, stroke=1)
            c.setFillColor('black')
            c.drawString(80, y_position - 10, f"{varnam}")
            c.drawString(300, y_position - 10, f"{varlabel}")
            
            y_position -= 35
    
    # 保存PDF
    c.save()
    print(f"注释PDF已创建: {output_path}")

def create_annotation_template():
    """创建注释模板示例"""
    output_path = "ecrf_annotation_template.pdf"
    c = canvas.Canvas(output_path, pagesize=A4)
    width, height = A4
    
    c.setFont("Helvetica", 14)
    c.drawString(50, height - 50, "eCRF Annotation Template")
    
    y_pos = height - 100
    
    # 示例注释框
    annotations = [
        ("DM (Demographics)", blue, "191, 255, 255"),
        ("DS (Disposition)", yellow, "255, 255, 150"),
        ("SC (Subject Characteristics)", green, "150, 255, 150"),
        ("VS (Vital Signs)", orange, "255, 190, 155")
    ]
    
    for text, color, rgb in annotations:
        c.setFillColor(color)
        c.rect(50, y_pos - 20, 250, 25, fill=1, stroke=1)
        c.setFillColor('black')
        c.drawString(60, y_pos - 15, text)
        c.drawString(350, y_pos - 15, rgb)
        y_pos -= 50
    
    c.save()
    print(f"模板已创建: {output_path}")

def main():
    """主函数"""
    excel_path = "dm_spec.xlsx"
    
    # 检查Excel文件是否存在
    if not os.path.exists(excel_path):
        print(f"Excel文件不存在: {excel_path}")
        return
    
    # 读取Excel数据
    excel_data = read_excel_data(excel_path)
    
    if excel_data is not None:
        # 显示Excel数据信息
        print("\nExcel数据预览:")
        print(excel_data.head())
        
        # 创建注释PDF
        output_pdf = "annotated_ecrf.pdf"
        create_annotated_pdf(excel_data, output_pdf)
        
        # 创建模板
        create_annotation_template()
        
        print("\n处理完成!")
        print(f"- 注释PDF: annotated_ecrf.pdf")
        print(f"- 模板PDF: ecrf_annotation_template.pdf")
    else:
        print("无法读取Excel数据")

if __name__ == "__main__":
    main()
