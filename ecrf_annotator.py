import pandas as pd
import json
import os

class eCRFAnnotator:
    """eCRF注释器类"""
    
    def __init__(self, excel_path):
        self.excel_path = excel_path
        self.excel_data = None
        self.annotation_data = None
        
    def read_excel_data(self):
        """读取Excel数据"""
        try:
            self.excel_data = pd.read_excel(self.excel_path)
            print(f"成功读取Excel文件: {self.excel_path}")
            print(f"数据行数: {len(self.excel_data)}")
            return True
        except Exception as e:
            print(f"读取Excel文件失败: {e}")
            return False
    
    def prepare_annotations(self):
        """准备注释数据"""
        if self.excel_data is None or self.excel_data.empty:
            return False
        
        # 获取基本信息
        domain = self.excel_data['DOMAIN'].iloc[0] if 'DOMAIN' in self.excel_data.columns else 'Unknown'
        obsclass = self.excel_data['OBSCLASS'].iloc[0] if 'OBSCLASS' in self.excel_data.columns else 'Unknown'
        
        # 创建注释数据结构
        self.annotation_data = {
            'domain_info': {
                'domain': domain,
                'obsclass': obsclass,
                'domain_label': f"{domain} ({obsclass})",
                'color_info': self._get_domain_color(domain)
            },
            'variables': []
        }
        
        # 添加变量信息
        for index, row in self.excel_data.iterrows():
            var_info = {
                'varnam': row['VARNAM'] if 'VARNAM' in row and pd.notna(row['VARNAM']) else 'N/A',
                'varlabel': row['VARLABEL'] if 'VARLABEL' in row and pd.notna(row['VARLABEL']) else 'N/A',
                'order': row['ORDER'] if 'ORDER' in row and pd.notna(row['ORDER']) else index + 1,
                'crf_page': row.get('CRF_P_NO', 'Unknown')
            }
            self.annotation_data['variables'].append(var_info)
        
        return True
    
    def _get_domain_color(self, domain):
        """根据Domain获取颜色信息"""
        color_mapping = {
            'DM': {
                'name': 'BLUE',
                'rgb': '191, 255, 255',
                'hex': '#BFFFFF',
                'description': 'Demographics'
            },
            'DS': {
                'name': 'YELLOW', 
                'rgb': '255, 255, 150',
                'hex': '#FFFF96',
                'description': 'Disposition'
            },
            'SC': {
                'name': 'GREEN',
                'rgb': '150, 255, 150',
                'hex': '#96FF96',
                'description': 'Subject Characteristics'
            },
            'VS': {
                'name': 'ORANGE',
                'rgb': '255, 190, 155',
                'hex': '#FFBE9B',
                'description': 'Vital Signs'
            }
        }
        
        return color_mapping.get(domain, {
            'name': 'BLUE',
            'rgb': '191, 255, 255',
            'hex': '#BFFFFF',
            'description': 'Unknown'
        })
    
    def generate_annotation_report(self):
        """生成注释报告"""
        if not self.annotation_data:
            return
        
        report = []
        report.append("="*80)
        report.append("eCRF 注释报告")
        report.append("="*80)
        
        # Domain信息
        domain_info = self.annotation_data['domain_info']
        report.append(f"\n【Domain信息】")
        report.append(f"Domain: {domain_info['domain']}")
        report.append(f"OBSCLASS: {domain_info['obsclass']}")
        report.append(f"注释文本: {domain_info['domain_label']}")
        report.append(f"颜色: {domain_info['color_info']['name']}")
        report.append(f"RGB值: {domain_info['color_info']['rgb']}")
        report.append(f"十六进制: {domain_info['color_info']['hex']}")
        
        # 变量信息
        report.append(f"\n【变量信息】(共{len(self.annotation_data['variables'])}个变量)")
        for i, var in enumerate(self.annotation_data['variables'], 1):
            report.append(f"{i:2d}. {var['varnam']:12s} -> {var['varlabel']}")
        
        # 注释说明
        report.append(f"\n【注释说明】")
        report.append(f"1. 在eCRF表单顶部添加Domain标识框:")
        report.append(f"   - 文本: {domain_info['domain_label']}")
        report.append(f"   - 背景色: {domain_info['color_info']['name']} ({domain_info['color_info']['rgb']})")
        report.append(f"   - 边框: 黑色实线")
        report.append(f"")
        report.append(f"2. 在相应字段位置添加变量注释:")
        for var in self.annotation_data['variables']:
            report.append(f"   - 字段'{var['varlabel']}'旁边注释: {var['varnam']}")
        report.append(f"   - 注释背景色: 同Domain颜色")
        report.append(f"   - 字体: 黑色")
        
        return "\n".join(report)
    
    def save_annotation_files(self, base_name="ecrf_annotation"):
        """保存注释文件"""
        if not self.annotation_data:
            print("没有注释数据可保存")
            return
        
        # 保存JSON文件
        json_file = f"{base_name}.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(self.annotation_data, f, ensure_ascii=False, indent=2)
        print(f"注释数据已保存: {json_file}")
        
        # 保存CSV文件
        csv_file = f"{base_name}.csv"
        self._create_annotation_csv(csv_file)
        
        # 保存报告文件
        report_file = f"{base_name}_report.txt"
        report = self.generate_annotation_report()
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        print(f"注释报告已保存: {report_file}")
        
        # 保存HTML预览文件
        html_file = f"{base_name}_preview.html"
        self._create_html_preview(html_file)
        
        return {
            'json': json_file,
            'csv': csv_file,
            'report': report_file,
            'html': html_file
        }
    
    def _create_annotation_csv(self, output_file):
        """创建注释信息的CSV文件"""
        rows = []
        
        # 添加Domain信息行
        domain_info = self.annotation_data['domain_info']
        rows.append({
            'Type': 'Domain',
            'Position': 'Form Header',
            'Text': domain_info['domain_label'],
            'Color': domain_info['color_info']['name'],
            'RGB': domain_info['color_info']['rgb'],
            'Hex': domain_info['color_info']['hex'],
            'Description': f"Domain identifier for {domain_info['color_info']['description']}"
        })
        
        # 添加变量信息行
        for var in self.annotation_data['variables']:
            rows.append({
                'Type': 'Variable',
                'Position': f"Field for {var['varlabel']}",
                'Text': var['varnam'],
                'Color': domain_info['color_info']['name'],
                'RGB': domain_info['color_info']['rgb'],
                'Hex': domain_info['color_info']['hex'],
                'Description': var['varlabel']
            })
        
        # 创建DataFrame并保存
        df = pd.DataFrame(rows)
        df.to_csv(output_file, index=False, encoding='utf-8-sig')
        print(f"注释CSV已保存: {output_file}")
    
    def _create_html_preview(self, output_file):
        """创建HTML预览文件"""
        domain_info = self.annotation_data['domain_info']
        color_hex = domain_info['color_info']['hex']
        
        html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>eCRF注释预览</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .domain-box {{ 
            background-color: {color_hex}; 
            border: 2px solid black; 
            padding: 10px; 
            margin: 10px 0; 
            display: inline-block;
            font-weight: bold;
        }}
        .variable-box {{ 
            background-color: {color_hex}; 
            border: 1px solid black; 
            padding: 5px; 
            margin: 5px; 
            display: inline-block;
            font-size: 12px;
        }}
        .section {{ margin: 20px 0; }}
        table {{ border-collapse: collapse; width: 100%; }}
        th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
        th {{ background-color: #f2f2f2; }}
    </style>
</head>
<body>
    <h1>eCRF注释预览</h1>
    
    <div class="section">
        <h2>Domain标识框预览</h2>
        <div class="domain-box">{domain_info['domain_label']}</div>
        <p>颜色: {domain_info['color_info']['name']} (RGB: {domain_info['color_info']['rgb']})</p>
    </div>
    
    <div class="section">
        <h2>变量注释框预览</h2>
"""
        
        for var in self.annotation_data['variables']:
            html_content += f'        <div class="variable-box">{var["varnam"]}</div>\n'
        
        html_content += f"""
    </div>
    
    <div class="section">
        <h2>注释详细信息</h2>
        <table>
            <tr><th>类型</th><th>位置</th><th>注释文本</th><th>说明</th></tr>
            <tr>
                <td>Domain</td>
                <td>表单头部</td>
                <td>{domain_info['domain_label']}</td>
                <td>Domain标识</td>
            </tr>
"""
        
        for var in self.annotation_data['variables']:
            html_content += f"""
            <tr>
                <td>Variable</td>
                <td>{var['varlabel']}字段</td>
                <td>{var['varnam']}</td>
                <td>{var['varlabel']}</td>
            </tr>
"""
        
        html_content += """
        </table>
    </div>
</body>
</html>
"""
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(html_content)
        print(f"HTML预览已保存: {output_file}")

def main():
    """主函数"""
    excel_path = "dm_spec.xlsx"
    
    print("eCRF注释器")
    print("="*50)
    
    # 检查Excel文件是否存在
    if not os.path.exists(excel_path):
        print(f"错误: Excel文件不存在 - {excel_path}")
        return
    
    # 创建注释器实例
    annotator = eCRFAnnotator(excel_path)
    
    # 读取Excel数据
    if not annotator.read_excel_data():
        return
    
    # 准备注释数据
    if not annotator.prepare_annotations():
        print("准备注释数据失败")
        return
    
    # 显示注释报告
    report = annotator.generate_annotation_report()
    print(report)
    
    # 保存注释文件
    files = annotator.save_annotation_files()
    
    print("\n" + "="*50)
    print("处理完成! 生成的文件:")
    for file_type, filename in files.items():
        print(f"- {file_type.upper()}: {filename}")
    
    print(f"\n请打开 {files['html']} 查看注释预览效果")

if __name__ == "__main__":
    main()
