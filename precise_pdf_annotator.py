import pandas as pd
import fitz  # PyMuPDF
import os
from pathlib import Path

class PrecisePDFAnnotator:
    """精确的PDF注释器 - 基于文字坐标定位"""
    
    def __init__(self, excel_path, pdf_path):
        self.excel_path = excel_path
        self.pdf_path = pdf_path
        self.excel_data = None
        self.annotation_data = None
        
        # 颜色规范 (fitz 0-1 浮点格式)
        self.domain_colors = {
            'DM': (0.75, 1, 1),      # 191,255,255
            'DS': (1, 1, 0.59),      # 255,255,150  
            'SC': (0.59, 1, 0.59),   # 150,255,150
            'VS': (1, 0.75, 0.61)    # 255,190,155
        }
    
    def read_excel_data(self):
        """解析数据源"""
        try:
            # 读取Excel文件
            df = pd.read_excel(self.excel_path)
            print(f"✓ 成功读取Excel文件: {self.excel_path}")
            print(f"  总记录数: {len(df)}")
            
            # 过滤 Domain == "DM" 且 OBSCLASS == "人口学资料" 的记录
            filtered_df = df[(df['DOMAIN'] == 'DM') & (df['OBSCLASS'] == '人口学资料')]
            print(f"  过滤后记录数: {len(filtered_df)}")
            
            if filtered_df.empty:
                print("❌ 没有找到符合条件的记录")
                return False
            
            # 提取 VARLABEL 与 VARNAM
            self.excel_data = filtered_df[['VARLABEL', 'VARNAM', 'ORDER']].copy()
            self.excel_data = self.excel_data.sort_values('ORDER')
            
            print("✓ 提取的变量信息:")
            for _, row in self.excel_data.iterrows():
                print(f"  {row['VARNAM']:12s} -> {row['VARLABEL']}")
            
            return True
            
        except Exception as e:
            print(f"❌ 读取Excel文件失败: {e}")
            return False
    
    def prepare_annotation_data(self):
        """准备注释数据"""
        if self.excel_data is None:
            return False
        
        self.annotation_data = {
            'domain': 'DM',
            'obsclass': '人口学资料',
            'domain_label': 'DM(人口学资料)',
            'color': self.domain_colors['DM'],
            'variables': []
        }
        
        for _, row in self.excel_data.iterrows():
            var_info = {
                'varnam': str(row['VARNAM']),
                'varlabel': str(row['VARLABEL']),
                'order': row['ORDER']
            }
            self.annotation_data['variables'].append(var_info)
        
        return True
    
    def find_text_coordinates(self, page, text):
        """在页面中查找文字坐标"""
        text_instances = page.search_for(text)
        if text_instances:
            return text_instances[0]  # 返回第一个匹配的矩形区域
        return None
    
    def add_domain_annotation(self, page, rect, domain_label, color):
        """添加Domain注释框"""
        # 在找到的文字左上方添加Domain标识框
        box_width = 120
        box_height = 25
        
        # 计算Domain框位置 (在找到文字的左上方)
        domain_rect = fitz.Rect(
            rect.x0 - 10,           # 左边稍微偏移
            rect.y0 - box_height - 5,  # 在文字上方
            rect.x0 - 10 + box_width,  # 右边界
            rect.y0 - 5             # 下边界
        )
        
        # 添加彩色矩形注释
        annot = page.add_rect_annot(domain_rect)
        annot.set_colors(stroke=color, fill=color)
        annot.set_border(width=2)
        annot.update()
        
        # 添加文字
        text_point = fitz.Point(domain_rect.x0 + 5, domain_rect.y0 + 15)
        page.insert_text(text_point, domain_label, fontsize=10, color=(0, 0, 0))
        
        print(f"✓ 已添加Domain注释: {domain_label} at {domain_rect}")
        return domain_rect
    
    def add_variable_annotation(self, page, rect, varnam, color):
        """添加变量注释框"""
        # 在找到的文字右侧添加变量注释
        box_width = 80
        box_height = 20
        
        # 计算变量框位置 (在找到文字的右侧)
        var_rect = fitz.Rect(
            rect.x1 + 10,           # 在文字右侧
            rect.y0,                # 与文字对齐
            rect.x1 + 10 + box_width,  # 右边界
            rect.y0 + box_height    # 下边界
        )
        
        # 添加彩色矩形注释
        annot = page.add_rect_annot(var_rect)
        annot.set_colors(stroke=color, fill=color)
        annot.set_border(width=1)
        annot.update()
        
        # 添加文字
        text_point = fitz.Point(var_rect.x0 + 3, var_rect.y0 + 12)
        page.insert_text(text_point, varnam, fontsize=8, color=(0, 0, 0))
        
        print(f"✓ 已添加变量注释: {varnam} at {var_rect}")
        return var_rect
    
    def annotate_pdf(self, output_path):
        """在PDF上添加注释"""
        try:
            # 打开PDF文件
            doc = fitz.open(self.pdf_path)
            print(f"✓ 成功打开PDF文件: {self.pdf_path}")
            print(f"  总页数: {len(doc)}")
            
            domain_color = self.annotation_data['color']
            domain_label = self.annotation_data['domain_label']
            
            # 搜索关键词列表
            search_terms = ['人口学资料', 'Demographics', 'DEMOGRAPHICS', '人口学', 'Demography']
            
            domain_added = False
            
            # 遍历所有页面
            for page_num in range(len(doc)):
                page = doc[page_num]
                print(f"\n处理第 {page_num + 1} 页...")
                
                # 1. 查找并添加Domain标识
                if not domain_added:
                    for term in search_terms:
                        rect = self.find_text_coordinates(page, term)
                        if rect:
                            print(f"✓ 在第 {page_num + 1} 页找到 '{term}' at {rect}")
                            self.add_domain_annotation(page, rect, domain_label, domain_color)
                            domain_added = True
                            break
                
                # 2. 查找并添加变量注释
                for var in self.annotation_data['variables']:
                    varlabel = var['varlabel']
                    varnam = var['varnam']
                    
                    # 尝试查找变量标签
                    rect = self.find_text_coordinates(page, varlabel)
                    if rect:
                        print(f"✓ 在第 {page_num + 1} 页找到变量 '{varlabel}' at {rect}")
                        self.add_variable_annotation(page, rect, varnam, domain_color)
                    else:
                        # 尝试查找部分匹配
                        words = varlabel.split()
                        for word in words:
                            if len(word) > 2:  # 只搜索长度大于2的词
                                rect = self.find_text_coordinates(page, word)
                                if rect:
                                    print(f"✓ 在第 {page_num + 1} 页找到部分匹配 '{word}' for {varlabel} at {rect}")
                                    self.add_variable_annotation(page, rect, varnam, domain_color)
                                    break
            
            # 保存注释后的PDF
            doc.save(output_path)
            doc.close()
            
            print(f"\n✓ 注释PDF已保存: {output_path}")
            return True
            
        except Exception as e:
            print(f"❌ 注释PDF失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def create_summary_report(self, output_path):
        """创建注释摘要报告"""
        report_lines = [
            "="*60,
            "eCRF注释摘要报告",
            "="*60,
            f"源Excel文件: {self.excel_path}",
            f"源PDF文件: {self.pdf_path}",
            f"输出PDF文件: {output_path}",
            "",
            f"Domain信息:",
            f"- Domain: {self.annotation_data['domain']}",
            f"- OBSCLASS: {self.annotation_data['obsclass']}",
            f"- 标识文字: {self.annotation_data['domain_label']}",
            f"- 颜色 (RGB): 191, 255, 255",
            "",
            f"变量注释 (共{len(self.annotation_data['variables'])}个):",
        ]
        
        for i, var in enumerate(self.annotation_data['variables'], 1):
            report_lines.append(f"  {i:2d}. {var['varnam']:12s} -> {var['varlabel']}")
        
        report_lines.extend([
            "",
            "注释说明:",
            "- Domain标识框: 在'人口学资料'文字左上方",
            "- 变量注释框: 在相应变量标签右侧",
            "- 所有注释使用相同的蓝色背景",
            "- 如果某些变量未找到，可能需要手动调整",
            "",
            "="*60
        ])
        
        report_file = output_path.replace('.pdf', '_report.txt')
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(report_lines))
        
        print(f"✓ 摘要报告已保存: {report_file}")

def main():
    """主函数"""
    excel_path = "dm_spec.xlsx"
    pdf_path = "HY1005-2023-2-P1_Unique eCRF_V3.0.pdf"
    
    print("精确PDF注释器")
    print("="*50)
    
    # 检查文件是否存在
    if not os.path.exists(excel_path):
        print(f"❌ Excel文件不存在: {excel_path}")
        return
    
    if not os.path.exists(pdf_path):
        print(f"❌ PDF文件不存在: {pdf_path}")
        return
    
    # 创建注释器
    annotator = PrecisePDFAnnotator(excel_path, pdf_path)
    
    # 1. 解析数据源
    print("\n1. 解析数据源...")
    if not annotator.read_excel_data():
        return
    
    # 2. 准备注释数据
    print("\n2. 准备注释数据...")
    if not annotator.prepare_annotation_data():
        print("❌ 准备注释数据失败")
        return
    
    print(f"✓ Domain: {annotator.annotation_data['domain']}")
    print(f"✓ OBSCLASS: {annotator.annotation_data['obsclass']}")
    print(f"✓ 变量数量: {len(annotator.annotation_data['variables'])}")
    
    # 3. 生成输出文件名
    pdf_stem = Path(pdf_path).stem
    output_path = f"{pdf_stem}_annotated.pdf"
    
    # 4. 执行注释
    print(f"\n3. 在PDF上添加注释...")
    if annotator.annotate_pdf(output_path):
        print(f"\n🎉 注释完成!")
        print(f"📁 输出文件: {output_path}")
        
        # 5. 生成摘要报告
        annotator.create_summary_report(output_path)
        
        print(f"\n📋 处理结果:")
        print(f"✓ 原始PDF: {pdf_path}")
        print(f"✓ 注释PDF: {output_path}")
        print(f"✓ 摘要报告: {output_path.replace('.pdf', '_report.txt')}")
        
    else:
        print("❌ 注释失败")

if __name__ == "__main__":
    main()
