import fitz  # PyMuPDF
import pandas as pd
import os
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import A4
from reportlab.lib.colors import Color

class PDFAnnotator:
    """PDF注释器 - 直接在PDF上添加注释"""
    
    def __init__(self, excel_path, pdf_path):
        self.excel_path = excel_path
        self.pdf_path = pdf_path
        self.excel_data = None
        self.annotation_data = None
        
        # 定义Domain颜色
        self.domain_colors = {
            'DM': {'rgb': (191/255, 1.0, 1.0), 'name': 'BLUE'},
            'DS': {'rgb': (1.0, 1.0, 150/255), 'name': 'YELLOW'},
            'SC': {'rgb': (150/255, 1.0, 150/255), 'name': 'GREEN'},
            'VS': {'rgb': (1.0, 190/255, 155/255), 'name': 'ORANGE'}
        }
    
    def read_excel_data(self):
        """读取Excel数据"""
        try:
            self.excel_data = pd.read_excel(self.excel_path)
            print(f"成功读取Excel文件: {self.excel_path}")
            print(f"数据行数: {len(self.excel_data)}")
            return True
        except Exception as e:
            print(f"读取Excel文件失败: {e}")
            return False
    
    def prepare_annotation_data(self):
        """准备注释数据"""
        if self.excel_data is None:
            return False
        
        domain = self.excel_data['DOMAIN'].iloc[0] if 'DOMAIN' in self.excel_data.columns else 'Unknown'
        obsclass = self.excel_data['OBSCLASS'].iloc[0] if 'OBSCLASS' in self.excel_data.columns else 'Unknown'
        
        self.annotation_data = {
            'domain': domain,
            'obsclass': obsclass,
            'domain_label': f"{domain} ({obsclass})",
            'color': self.domain_colors.get(domain, {'rgb': (191/255, 1.0, 1.0), 'name': 'BLUE'}),
            'variables': []
        }
        
        for _, row in self.excel_data.iterrows():
            var_info = {
                'varnam': row.get('VARNAM', 'N/A'),
                'varlabel': row.get('VARLABEL', 'N/A'),
                'order': row.get('ORDER', 0)
            }
            self.annotation_data['variables'].append(var_info)
        
        return True
    
    def create_annotation_overlay(self, output_path):
        """创建注释覆盖层PDF"""
        try:
            # 创建新的PDF画布
            c = canvas.Canvas(output_path, pagesize=A4)
            width, height = A4
            
            # 设置字体
            c.setFont("Helvetica-Bold", 14)
            
            # 获取颜色
            color_rgb = self.annotation_data['color']['rgb']
            
            # 添加Domain标识框 (在页面顶部)
            domain_x, domain_y = 50, height - 100
            domain_width, domain_height = 200, 30
            
            # 绘制Domain框
            c.setFillColor(Color(color_rgb[0], color_rgb[1], color_rgb[2]))
            c.setStrokeColor('black')
            c.setLineWidth(2)
            c.rect(domain_x, domain_y, domain_width, domain_height, fill=1, stroke=1)
            
            # 添加Domain文本
            c.setFillColor('black')
            c.drawString(domain_x + 10, domain_y + 10, self.annotation_data['domain_label'])
            
            # 添加RGB值标注
            c.setFont("Helvetica", 10)
            rgb_text = f"{int(color_rgb[0]*255)}, {int(color_rgb[1]*255)}, {int(color_rgb[2]*255)}"
            c.drawString(domain_x + domain_width + 20, domain_y + 15, rgb_text)
            
            # 添加变量注释框
            c.setFont("Helvetica", 10)
            var_y = domain_y - 60
            
            for i, var in enumerate(self.annotation_data['variables']):
                if var_y < 100:  # 如果空间不够，创建新页面
                    c.showPage()
                    c.setFont("Helvetica", 10)
                    var_y = height - 50
                
                # 变量框位置
                var_x = 70 + (i % 4) * 120  # 每行4个变量框
                if i > 0 and i % 4 == 0:
                    var_y -= 40
                
                var_width, var_height = 100, 25
                
                # 绘制变量框
                c.setFillColor(Color(color_rgb[0], color_rgb[1], color_rgb[2]))
                c.setStrokeColor('black')
                c.setLineWidth(1)
                c.rect(var_x, var_y, var_width, var_height, fill=1, stroke=1)
                
                # 添加变量名
                c.setFillColor('black')
                c.drawString(var_x + 5, var_y + 8, var['varnam'])
                
                # 添加变量标签 (在框下方)
                c.setFont("Helvetica", 8)
                c.drawString(var_x, var_y - 15, var['varlabel'][:15] + "..." if len(var['varlabel']) > 15 else var['varlabel'])
                c.setFont("Helvetica", 10)
            
            # 保存覆盖层PDF
            c.save()
            print(f"注释覆盖层已创建: {output_path}")
            return True
            
        except Exception as e:
            print(f"创建注释覆盖层失败: {e}")
            return False
    
    def merge_pdfs(self, original_pdf, overlay_pdf, output_pdf):
        """合并原始PDF和注释覆盖层"""
        try:
            # 打开原始PDF
            original_doc = fitz.open(original_pdf)
            
            # 打开覆盖层PDF
            overlay_doc = fitz.open(overlay_pdf)
            
            # 创建新的PDF文档
            output_doc = fitz.open()
            
            # 处理每一页
            for page_num in range(len(original_doc)):
                # 获取原始页面
                original_page = original_doc[page_num]
                
                # 创建新页面
                new_page = output_doc.new_page(width=original_page.rect.width, 
                                             height=original_page.rect.height)
                
                # 插入原始页面内容
                new_page.show_pdf_page(original_page.rect, original_doc, page_num)
                
                # 如果有覆盖层页面，添加注释
                if page_num < len(overlay_doc):
                    overlay_page = overlay_doc[page_num]
                    new_page.show_pdf_page(original_page.rect, overlay_doc, page_num)
            
            # 保存合并后的PDF
            output_doc.save(output_pdf)
            output_doc.close()
            original_doc.close()
            overlay_doc.close()
            
            print(f"PDF合并完成: {output_pdf}")
            return True
            
        except Exception as e:
            print(f"PDF合并失败: {e}")
            return False
    
    def create_annotated_pdf_simple(self, output_path):
        """创建简单的注释PDF（不合并原始PDF）"""
        try:
            # 创建新的PDF
            c = canvas.Canvas(output_path, pagesize=A4)
            width, height = A4
            
            # 页面标题
            c.setFont("Helvetica-Bold", 16)
            c.drawString(50, height - 50, "eCRF注释示例")
            
            # 说明文字
            c.setFont("Helvetica", 12)
            c.drawString(50, height - 80, f"基于 {self.excel_path} 生成的注释")
            c.drawString(50, height - 100, f"原始PDF: {self.pdf_path}")
            
            # 获取颜色
            color_rgb = self.annotation_data['color']['rgb']
            
            # Domain标识框
            domain_y = height - 150
            c.setFont("Helvetica-Bold", 14)
            c.setFillColor(Color(color_rgb[0], color_rgb[1], color_rgb[2]))
            c.setStrokeColor('black')
            c.setLineWidth(2)
            c.rect(50, domain_y, 250, 35, fill=1, stroke=1)
            
            c.setFillColor('black')
            c.drawString(60, domain_y + 12, self.annotation_data['domain_label'])
            
            # RGB值
            c.setFont("Helvetica", 10)
            rgb_text = f"RGB: {int(color_rgb[0]*255)}, {int(color_rgb[1]*255)}, {int(color_rgb[2]*255)}"
            c.drawString(320, domain_y + 17, rgb_text)
            
            # 变量注释框
            c.setFont("Helvetica-Bold", 12)
            c.drawString(50, domain_y - 40, "变量注释:")
            
            var_y = domain_y - 70
            for i, var in enumerate(self.annotation_data['variables']):
                if var_y < 100:
                    c.showPage()
                    c.setFont("Helvetica", 10)
                    var_y = height - 50
                
                # 变量框
                var_x = 70 + (i % 3) * 150
                if i > 0 and i % 3 == 0:
                    var_y -= 60
                
                # 绘制变量框
                c.setFillColor(Color(color_rgb[0], color_rgb[1], color_rgb[2]))
                c.setStrokeColor('black')
                c.setLineWidth(1)
                c.rect(var_x, var_y, 120, 30, fill=1, stroke=1)
                
                # 变量名
                c.setFillColor('black')
                c.setFont("Helvetica-Bold", 10)
                c.drawString(var_x + 5, var_y + 15, var['varnam'])
                
                # 变量标签
                c.setFont("Helvetica", 8)
                c.drawString(var_x + 5, var_y + 5, var['varlabel'])
                
                # 在框下方显示完整标签
                c.setFont("Helvetica", 9)
                c.drawString(var_x, var_y - 15, f"标签: {var['varlabel']}")
            
            # 添加使用说明
            c.setFont("Helvetica", 10)
            instructions_y = 150
            instructions = [
                "使用说明:",
                "1. 将上述Domain标识框添加到eCRF表单顶部",
                "2. 在相应的数据字段旁边添加对应的变量注释框",
                "3. 使用相同的背景颜色保持一致性",
                "4. 确保注释框不遮挡原有的表单内容"
            ]
            
            for instruction in instructions:
                c.drawString(50, instructions_y, instruction)
                instructions_y -= 20
            
            c.save()
            print(f"注释PDF已创建: {output_path}")
            return True
            
        except Exception as e:
            print(f"创建注释PDF失败: {e}")
            return False

def main():
    """主函数"""
    excel_path = "dm_spec.xlsx"
    pdf_path = "HY1005-2023-2-P1_Unique eCRF_V3.0.pdf"
    
    print("PDF注释器")
    print("="*50)
    
    # 检查文件是否存在
    if not os.path.exists(excel_path):
        print(f"错误: Excel文件不存在 - {excel_path}")
        return
    
    if not os.path.exists(pdf_path):
        print(f"警告: 原始PDF文件不存在 - {pdf_path}")
        print("将创建独立的注释示例PDF")
    
    # 创建注释器
    annotator = PDFAnnotator(excel_path, pdf_path)
    
    # 读取Excel数据
    if not annotator.read_excel_data():
        return
    
    # 准备注释数据
    if not annotator.prepare_annotation_data():
        print("准备注释数据失败")
        return
    
    # 显示注释信息
    print(f"\n注释信息:")
    print(f"Domain: {annotator.annotation_data['domain']}")
    print(f"OBSCLASS: {annotator.annotation_data['obsclass']}")
    print(f"Domain标签: {annotator.annotation_data['domain_label']}")
    print(f"颜色: {annotator.annotation_data['color']['name']}")
    print(f"变量数量: {len(annotator.annotation_data['variables'])}")
    
    # 创建注释PDF
    output_pdf = "annotated_ecrf.pdf"
    
    if annotator.create_annotated_pdf_simple(output_pdf):
        print(f"\n成功创建注释PDF: {output_pdf}")
        print("请查看生成的PDF文件，其中包含了所有需要添加到eCRF的注释框样式。")
    else:
        print("创建注释PDF失败")

if __name__ == "__main__":
    main()
