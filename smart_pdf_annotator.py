import pandas as pd
import os
from pathlib import Path
import json

class SmartPDFAnnotator:
    """智能PDF注释器 - 生成精确的注释指令"""
    
    def __init__(self, excel_path, pdf_path):
        self.excel_path = excel_path
        self.pdf_path = pdf_path
        self.excel_data = None
        self.annotation_data = None
        
        # 颜色规范
        self.domain_colors = {
            'DM': {'rgb': '191,255,255', 'fitz': '(0.75,1,1)', 'hex': '#BFFFFF'},
            'DS': {'rgb': '255,255,150', 'fitz': '(1,1,0.59)', 'hex': '#FFFF96'},
            'SC': {'rgb': '150,255,150', 'fitz': '(0.59,1,0.59)', 'hex': '#96FF96'},
            'VS': {'rgb': '255,190,155', 'fitz': '(1,0.75,0.61)', 'hex': '#FFBE9B'}
        }
    
    def read_excel_data(self):
        """解析数据源"""
        try:
            # 读取Excel文件
            df = pd.read_excel(self.excel_path)
            print(f"成功读取Excel文件: {self.excel_path}")
            print(f"  总记录数: {len(df)}")
            
            # 显示所有列名
            print(f"  列名: {list(df.columns)}")
            
            # 显示前几行数据
            print(f"  前3行数据:")
            for i, row in df.head(3).iterrows():
                print(f"    行{i+1}: DOMAIN={row.get('DOMAIN', 'N/A')}, OBSCLASS={row.get('OBSCLASS', 'N/A')}")
            
            # 过滤 Domain == "DM" 且 OBSCLASS == "人口学资料" 的记录
            if 'DOMAIN' in df.columns and 'OBSCLASS' in df.columns:
                filtered_df = df[(df['DOMAIN'] == 'DM') & (df['OBSCLASS'] == '人口学资料')]
                print(f"  过滤后记录数: {len(filtered_df)}")
            else:
                # 如果列名不匹配，使用所有记录
                filtered_df = df
                print(f"  未找到DOMAIN/OBSCLASS列，使用所有记录: {len(filtered_df)}")
            
            if filtered_df.empty:
                print("没有找到符合条件的记录")
                return False
            
            # 提取 VARLABEL 与 VARNAM
            required_cols = ['VARLABEL', 'VARNAM']
            available_cols = [col for col in required_cols if col in filtered_df.columns]
            
            if not available_cols:
                print("未找到VARLABEL或VARNAM列")
                return False
            
            # 添加ORDER列如果存在
            if 'ORDER' in filtered_df.columns:
                available_cols.append('ORDER')
                self.excel_data = filtered_df[available_cols].copy()
                self.excel_data = self.excel_data.sort_values('ORDER')
            else:
                self.excel_data = filtered_df[available_cols].copy()
            
            print("提取的变量信息:")
            for i, row in self.excel_data.iterrows():
                varnam = row.get('VARNAM', 'N/A')
                varlabel = row.get('VARLABEL', 'N/A')
                print(f"  {varnam:12s} -> {varlabel}")
            
            return True
            
        except Exception as e:
            print(f"读取Excel文件失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def prepare_annotation_data(self):
        """准备注释数据"""
        if self.excel_data is None:
            return False
        
        self.annotation_data = {
            'domain': 'DM',
            'obsclass': '人口学资料',
            'domain_label': 'DM(人口学资料)',
            'color_info': self.domain_colors['DM'],
            'variables': []
        }
        
        for i, row in self.excel_data.iterrows():
            var_info = {
                'varnam': str(row.get('VARNAM', 'N/A')),
                'varlabel': str(row.get('VARLABEL', 'N/A')),
                'order': row.get('ORDER', i+1)
            }
            self.annotation_data['variables'].append(var_info)
        
        return True
    
    def generate_annotation_script(self, output_path):
        """生成PyMuPDF注释脚本"""
        script_content = f'''#!/usr/bin/env python3
"""
自动生成的PDF注释脚本
使用PyMuPDF (fitz) 在PDF上添加精确注释
"""

import fitz  # PyMuPDF
import os

def annotate_pdf():
    """在PDF上添加注释"""
    
    # 文件路径
    pdf_path = "{self.pdf_path}"
    output_path = "{Path(self.pdf_path).stem}_annotated.pdf"
    
    # 颜色定义 (fitz 0-1 浮点格式)
    domain_color = {self.annotation_data['color_info']['fitz']}  # DM域颜色
    
    try:
        # 打开PDF文件
        doc = fitz.open(pdf_path)
        print(f"✓ 成功打开PDF: {{pdf_path}}")
        print(f"  总页数: {{len(doc)}}")
        
        # 搜索关键词
        search_terms = ['人口学资料', 'Demographics', 'DEMOGRAPHICS', '人口学', 'Demography']
        domain_added = False
        
        # 遍历所有页面
        for page_num in range(len(doc)):
            page = doc[page_num]
            print(f"\\n处理第 {{page_num + 1}} 页...")
            
            # 1. 查找并添加Domain标识
            if not domain_added:
                for term in search_terms:
                    text_instances = page.search_for(term)
                    if text_instances:
                        rect = text_instances[0]  # 第一个匹配
                        print(f"✓ 找到 '{{term}}' at {{rect}}")
                        
                        # 在左上方添加Domain框
                        domain_rect = fitz.Rect(
                            rect.x0 - 10,
                            rect.y0 - 30,
                            rect.x0 + 110,
                            rect.y0 - 5
                        )
                        
                        # 添加彩色矩形
                        annot = page.add_rect_annot(domain_rect)
                        annot.set_colors(stroke=domain_color, fill=domain_color)
                        annot.set_border(width=2)
                        annot.update()
                        
                        # 添加文字
                        text_point = fitz.Point(domain_rect.x0 + 5, domain_rect.y0 + 15)
                        page.insert_text(text_point, "{self.annotation_data['domain_label']}", 
                                       fontsize=10, color=(0, 0, 0))
                        
                        print(f"✓ 已添加Domain注释: {self.annotation_data['domain_label']}")
                        domain_added = True
                        break
            
            # 2. 查找并添加变量注释
'''
        
        # 添加变量注释代码
        for var in self.annotation_data['variables']:
            script_content += f'''
            # 查找变量: {var['varnam']} -> {var['varlabel']}
            varlabel = "{var['varlabel']}"
            varnam = "{var['varnam']}"
            
            text_instances = page.search_for(varlabel)
            if text_instances:
                rect = text_instances[0]
                print(f"✓ 找到变量 '{{varlabel}}' at {{rect}}")
                
                # 在右侧添加变量框
                var_rect = fitz.Rect(
                    rect.x1 + 10,
                    rect.y0,
                    rect.x1 + 90,
                    rect.y0 + 20
                )
                
                # 添加彩色矩形
                annot = page.add_rect_annot(var_rect)
                annot.set_colors(stroke=domain_color, fill=domain_color)
                annot.set_border(width=1)
                annot.update()
                
                # 添加文字
                text_point = fitz.Point(var_rect.x0 + 3, var_rect.y0 + 12)
                page.insert_text(text_point, varnam, fontsize=8, color=(0, 0, 0))
                
                print(f"✓ 已添加变量注释: {{varnam}}")
            else:
                # 尝试部分匹配
                words = varlabel.split()
                for word in words:
                    if len(word) > 2:
                        text_instances = page.search_for(word)
                        if text_instances:
                            rect = text_instances[0]
                            print(f"✓ 部分匹配 '{{word}}' for {{varlabel}} at {{rect}}")
                            
                            var_rect = fitz.Rect(rect.x1 + 10, rect.y0, rect.x1 + 90, rect.y0 + 20)
                            annot = page.add_rect_annot(var_rect)
                            annot.set_colors(stroke=domain_color, fill=domain_color)
                            annot.set_border(width=1)
                            annot.update()
                            
                            text_point = fitz.Point(var_rect.x0 + 3, var_rect.y0 + 12)
                            page.insert_text(text_point, varnam, fontsize=8, color=(0, 0, 0))
                            print(f"✓ 已添加变量注释: {{varnam}}")
                            break
                else:
                    print(f"⚠ 未找到变量: {{varlabel}}")
'''
        
        script_content += f'''
        
        # 保存注释后的PDF
        doc.save(output_path)
        doc.close()
        
        print(f"\\n🎉 注释完成!")
        print(f"📁 输出文件: {{output_path}}")
        
        return True
        
    except Exception as e:
        print(f"❌ 注释失败: {{e}}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("PDF注释脚本")
    print("="*50)
    
    # 检查依赖
    try:
        import fitz
        print("✓ PyMuPDF (fitz) 已安装")
    except ImportError:
        print("❌ 请先安装PyMuPDF: pip install pymupdf")
        exit(1)
    
    # 执行注释
    annotate_pdf()
'''
        
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(script_content)
        
        print(f"PyMuPDF注释脚本已生成: {output_path}")
        return output_path
    
    def generate_manual_guide(self, output_path):
        """生成手动注释指南"""
        guide_content = f"""
# eCRF手动注释指南

## 基本信息
- 源Excel文件: {self.excel_path}
- 源PDF文件: {self.pdf_path}
- Domain: {self.annotation_data['domain']}
- OBSCLASS: {self.annotation_data['obsclass']}

## 颜色规范
- Domain: DM
- RGB颜色: {self.annotation_data['color_info']['rgb']}
- 十六进制: {self.annotation_data['color_info']['hex']}

## 注释步骤

### 1. 添加Domain标识
在PDF中搜索以下关键词之一：
- "人口学资料"
- "Demographics" 
- "DEMOGRAPHICS"
- "人口学"

找到后，在该文字的左上方添加注释框：
- 文字内容: {self.annotation_data['domain_label']}
- 背景颜色: RGB({self.annotation_data['color_info']['rgb']})
- 边框: 2px 黑色
- 字体: 10px 黑色

### 2. 添加变量注释
"""
        
        for i, var in enumerate(self.annotation_data['variables'], 1):
            guide_content += f"""
#### 变量 {i}: {var['varnam']}
- 搜索文字: "{var['varlabel']}"
- 在找到的文字右侧添加注释框
- 注释内容: {var['varnam']}
- 背景颜色: RGB({self.annotation_data['color_info']['rgb']})
- 边框: 1px 黑色
- 字体: 8px 黑色
"""
        
        guide_content += f"""

## 技术规格总结
- Domain框尺寸: 约120x25像素
- 变量框尺寸: 约80x20像素
- 所有注释使用相同背景色: {self.annotation_data['color_info']['hex']}
- Domain框边框: 2px
- 变量框边框: 1px

## 注意事项
1. 如果某些变量标签未找到，尝试搜索标签中的关键词
2. 确保注释不遮挡原有内容
3. 保持所有注释的颜色一致性
4. 建议使用Adobe Acrobat或Foxit PDF Editor等专业工具

## 输出文件命名
建议将注释后的文件命名为: {Path(self.pdf_path).stem}_annotated.pdf
"""
        
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(guide_content)
        
        print(f"手动注释指南已生成: {output_path}")
        return output_path
    
    def generate_json_data(self, output_path):
        """生成JSON格式的注释数据"""
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(self.annotation_data, f, ensure_ascii=False, indent=2)
        
        print(f"JSON数据已生成: {output_path}")
        return output_path

def main():
    """主函数"""
    excel_path = "dm_spec.xlsx"
    pdf_path = "HY1005-2023-2-P1_Unique eCRF_V3.0.pdf"
    
    print("智能PDF注释器")
    print("="*50)
    
    # 检查文件是否存在
    if not os.path.exists(excel_path):
        print(f"Excel文件不存在: {excel_path}")
        return

    if not os.path.exists(pdf_path):
        print(f"PDF文件不存在: {pdf_path}")
        print(f"   将生成通用注释指南")
        pdf_path = "eCRF_template.pdf"  # 使用通用名称
    
    # 创建注释器
    annotator = SmartPDFAnnotator(excel_path, pdf_path)
    
    # 1. 解析数据源
    print("\\n1. 解析数据源...")
    if not annotator.read_excel_data():
        return
    
    # 2. 准备注释数据
    print("\\n2. 准备注释数据...")
    if not annotator.prepare_annotation_data():
        print("准备注释数据失败")
        return

    print(f"Domain: {annotator.annotation_data['domain']}")
    print(f"OBSCLASS: {annotator.annotation_data['obsclass']}")
    print(f"变量数量: {len(annotator.annotation_data['variables'])}")
    
    # 3. 生成输出文件
    print("\\n3. 生成输出文件...")
    
    base_name = Path(pdf_path).stem
    
    # 生成PyMuPDF脚本
    script_file = f"{base_name}_annotation_script.py"
    annotator.generate_annotation_script(script_file)
    
    # 生成手动指南
    guide_file = f"{base_name}_manual_guide.md"
    annotator.generate_manual_guide(guide_file)
    
    # 生成JSON数据
    json_file = f"{base_name}_annotation_data.json"
    annotator.generate_json_data(json_file)
    
    print(f"\\n生成完成!")
    print(f"输出文件:")
    print(f"  {script_file} - PyMuPDF自动注释脚本")
    print(f"  {guide_file} - 手动注释指南")
    print(f"  {json_file} - 注释数据")

    print(f"\\n使用说明:")
    print(f"方法1 (自动): 安装PyMuPDF后运行 python {script_file}")
    print(f"方法2 (手动): 参考 {guide_file} 使用PDF编辑器手动添加")

if __name__ == "__main__":
    main()
