#!/usr/bin/env python3
"""
终极完美版PDF注释脚本
解决颜色框覆盖问题的正确方法
"""

import fitz  # PyMuPDF
import os

def annotate_pdf_ultimate():
    """终极完美版注释 - 正确的方法"""
    
    pdf_path = "HY1005-2023-2-P1_Unique eCRF_V3.0.pdf"
    output_path = "HY1005-2023-2-P1_Unique eCRF_V3.0_ULTIMATE_annotated.pdf"
    
    # DM域颜色
    domain_color = (0.75, 1, 1)  # RGB(191,255,255)
    
    # 严格按照Excel数据的字段映射
    field_mappings = {
        "出生日期": "BRTHDTC",
        "年龄": "AGE",
        "周岁": "AGEU",
        "性别": "SEX"
    }
    
    try:
        doc = fitz.open(pdf_path)
        print(f"开始终极完美注释...")
        print(f"解决方案: 先绘制背景，再添加文字，确保正确的图层顺序")
        
        domain_added = False
        
        # 遍历所有页面
        for page_num in range(len(doc)):
            page = doc[page_num]
            print(f"\n处理第 {page_num + 1} 页...")
            
            # 1. 添加Domain标识
            if not domain_added:
                if page_num >= 6:  # 从第7页开始查找表单
                    text_instances = page.search_for("人口学资料")
                    for rect in text_instances:
                        # 检查是否在表单位置
                        if rect.y0 < page.rect.height * 0.3:
                            print(f"找到表单标题 '人口学资料' at {rect}")
                            
                            # 计算Domain标识位置
                            domain_rect = fitz.Rect(
                                rect.x0 - 130,
                                rect.y0 - 5,
                                rect.x0 - 10,
                                rect.y0 + 15
                            )
                            
                            # 方法: 使用简化的自由文本注释
                            try:
                                text_annot = page.add_freetext_annot(
                                    domain_rect,
                                    "DM(人口学资料)",
                                    fontsize=10,
                                    text_color=(0, 0, 0),     # 黑色文字
                                    fill_color=domain_color   # 彩色背景
                                )
                                text_annot.set_border(width=2, style="solid")
                                text_annot.update()
                                
                                print(f"已添加Domain标识: DM(人口学资料) at {domain_rect}")
                                domain_added = True
                                break
                                
                            except Exception as e:
                                print(f"自由文本注释失败: {e}")
                                # 备用方案: 使用传统方法但优化图层
                                self.add_annotation_with_background(page, domain_rect, "DM(人口学资料)", domain_color, 10)
                                domain_added = True
                                break
            
            # 2. 添加变量注释
            for pdf_field_name, varnam in field_mappings.items():
                text_instances = page.search_for(pdf_field_name)
                if text_instances:
                    rect = text_instances[0]
                    print(f"找到字段 '{pdf_field_name}' at {rect}")
                    
                    # 计算注释位置
                    if pdf_field_name == "周岁":
                        var_rect = fitz.Rect(
                            rect.x1 + 5,
                            rect.y0 - 2,
                            rect.x1 + 50,
                            rect.y0 + 15
                        )
                    else:
                        var_rect = fitz.Rect(
                            rect.x1 + 10,
                            rect.y0 - 2,
                            rect.x1 + 70,
                            rect.y0 + 16
                        )
                    
                    # 检查边界
                    if var_rect.x1 > page.rect.width - 20:
                        var_rect = fitz.Rect(
                            rect.x0 - 70,
                            rect.y0 - 2,
                            rect.x0 - 10,
                            rect.y0 + 16
                        )
                    
                    # 添加变量注释
                    try:
                        text_annot = page.add_freetext_annot(
                            var_rect,
                            varnam,
                            fontsize=9,
                            text_color=(0, 0, 0),     # 黑色文字
                            fill_color=domain_color   # 彩色背景
                        )
                        text_annot.set_border(width=1, style="solid")
                        text_annot.update()
                        
                        print(f"已添加变量注释: {varnam} for '{pdf_field_name}' at {var_rect}")
                        
                    except Exception as e:
                        print(f"变量注释失败: {e}")
                        # 备用方案
                        add_annotation_with_background(page, var_rect, varnam, domain_color, 9)
        
        # 保存终极版本
        doc.save(output_path)
        doc.close()
        
        print(f"\n终极完美注释完成!")
        print(f"输出文件: {output_path}")
        
        return True
        
    except Exception as e:
        print(f"注释失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def add_annotation_with_background(page, rect, text, bg_color, fontsize):
    """备用方案: 优化的背景+文字方法"""
    
    # 1. 先添加背景矩形
    bg_annot = page.add_rect_annot(rect)
    bg_annot.set_colors(stroke=(0, 0, 0), fill=bg_color)
    bg_annot.set_border(width=1)
    bg_annot.set_opacity(0.9)  # 稍微透明，避免完全遮挡
    bg_annot.update()
    
    # 2. 再添加文字 - 使用文字注释而不是insert_text
    text_rect = fitz.Rect(rect.x0 + 2, rect.y0 + 2, rect.x1 - 2, rect.y1 - 2)
    
    try:
        # 尝试使用文字注释
        text_annot = page.add_text_annot(
            fitz.Point(rect.x0 + 3, rect.y0 + fontsize + 2),
            text
        )
        text_annot.set_info(content=text)
        text_annot.update()
        
    except:
        # 最后备用: 直接插入文字
        text_point = fitz.Point(rect.x0 + 3, rect.y0 + fontsize + 2)
        page.insert_text(text_point, text, fontsize=fontsize, color=(0, 0, 0))
    
    print(f"使用备用方案添加注释: {text}")

if __name__ == "__main__":
    print("终极完美版PDF注释脚本")
    print("="*60)
    
    print("解决颜色框覆盖问题的根本原因:")
    print("1. 问题: add_rect_annot + insert_text 创建分离的元素")
    print("2. 解决: 使用 add_freetext_annot 创建一体化注释")
    print("3. 备用: 优化图层顺序和透明度")
    
    # 检查PDF文件
    pdf_path = "HY1005-2023-2-P1_Unique eCRF_V3.0.pdf"
    if not os.path.exists(pdf_path):
        print(f"错误: PDF文件不存在 - {pdf_path}")
        exit(1)
    
    print("\n执行终极完美注释...")
    success = annotate_pdf_ultimate()
    
    if success:
        print("\n🎉 终极解决方案完成!")
        print("解决了颜色框覆盖问题:")
        print("✅ 文字在颜色框里，而不是颜色框覆盖文字")
        print("✅ 注释作为一个整体，不分离")
        print("✅ 不遮挡PDF原有内容")
        print("\n📁 终极完美文件: HY1005-2023-2-P1_Unique eCRF_V3.0_ULTIMATE_annotated.pdf")
    else:
        print("\n❌ 注释失败，请检查错误信息")
