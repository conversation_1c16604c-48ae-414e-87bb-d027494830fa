#!/usr/bin/env python3
"""
自动适配宽度版PDF注释脚本
颜色框根据字体长短自动调整
"""

import fitz  # PyMuPDF
import os

def calculate_text_width(text, fontsize):
    """计算文字的实际宽度"""
    # 根据字体大小和字符数量估算宽度
    # 英文字符约为字体大小的0.6倍，中文字符约为字体大小的1倍
    width = 0
    for char in text:
        if ord(char) < 128:  # ASCII字符（英文）
            width += fontsize * 0.6
        else:  # 非ASCII字符（中文等）
            width += fontsize * 1.0
    
    # 添加一些边距
    padding = 6
    return width + padding

def annotate_pdf_auto_fit_width():
    """自动适配宽度版注释"""
    
    pdf_path = "HY1005-2023-2-P1_Unique eCRF_V3.0.pdf"
    output_path = "HY1005-2023-2-P1_Unique eCRF_V3.0_AUTO_FIT_WIDTH_annotated.pdf"
    
    # DM域颜色
    domain_color = (0.75, 1, 1)  # RGB(191,255,255)
    
    # 严格按照Excel数据的字段映射
    field_mappings = {
        "出生日期": "BRTHDTC",
        "年龄": "AGE",
        "周岁": "AGEU",
        "性别": "SEX"
    }
    
    try:
        doc = fitz.open(pdf_path)
        print(f"开始自动适配宽度注释...")
        
        # 只处理第7页
        target_page = 6  # 第7页，索引为6
        page = doc[target_page]
        print(f"\n只处理第 {target_page + 1} 页...")
        print(f"页面宽度: {page.rect.width}")
        
        # 安全边距
        SAFE_MARGIN = 50
        
        # 1. 添加Domain标识
        text_instances = page.search_for("人口学资料")
        for rect in text_instances:
            if rect.y0 < page.rect.height * 0.3:
                print(f"找到表单标题 '人口学资料' at {rect}")
                
                # Domain标识也使用自动宽度
                domain_text = "DM(人口学资料)"
                domain_fontsize = 10
                domain_width = calculate_text_width(domain_text, domain_fontsize)
                
                domain_rect = fitz.Rect(
                    rect.x0,
                    rect.y0 - 25,
                    rect.x0 + domain_width,
                    rect.y0 - 5
                )
                
                print(f"Domain文字: '{domain_text}', 字体大小: {domain_fontsize}, 计算宽度: {domain_width:.1f}")
                
                try:
                    text_annot = page.add_freetext_annot(
                        domain_rect,
                        domain_text,
                        fontsize=domain_fontsize,
                        text_color=(0, 0, 0),
                        fill_color=domain_color
                    )
                    text_annot.set_border(width=2, style="solid")
                    text_annot.update()
                    
                    print(f"已添加Domain标识: {domain_text} at {domain_rect}")
                    break
                    
                except Exception as e:
                    print(f"Domain注释失败: {e}")
                    break
        
        # 2. 添加变量注释 - 每个都自动适配宽度
        for pdf_field_name, varnam in field_mappings.items():
            text_instances = page.search_for(pdf_field_name)
            if text_instances:
                rect = text_instances[0]
                print(f"\n找到字段 '{pdf_field_name}' at {rect}")
                
                # 计算变量名的实际宽度
                var_fontsize = 9
                var_width = calculate_text_width(varnam, var_fontsize)
                
                print(f"变量名: '{varnam}', 字体大小: {var_fontsize}, 计算宽度: {var_width:.1f}")
                
                if pdf_field_name == "周岁":
                    print(f"AGEU特殊处理 - 自动适配宽度...")
                    
                    # 计算AGEU的最佳位置
                    # 优先在"周岁"上方
                    start_x = rect.x0
                    end_x = start_x + var_width
                    
                    # 检查是否超出页面
                    max_right = page.rect.width - SAFE_MARGIN
                    if end_x > max_right:
                        # 调整起始位置
                        end_x = max_right
                        start_x = end_x - var_width
                        print(f"调整AGEU位置避免出界")
                    
                    var_rect = fitz.Rect(
                        start_x,
                        rect.y0 - 18,  # 在"周岁"上方
                        end_x,
                        rect.y0 - 3
                    )
                    
                    print(f"AGEU自动适配: 起始{start_x:.1f}, 结束{end_x:.1f}, 宽度{var_width:.1f}")
                    print(f"距离右边缘: {page.rect.width - end_x:.1f} 像素")
                
                else:
                    # 其他字段的自动适配
                    start_x = rect.x1 + 10
                    end_x = start_x + var_width
                    
                    # 检查边界
                    max_right = page.rect.width - SAFE_MARGIN
                    if end_x > max_right:
                        # 调整到左侧
                        end_x = rect.x0 - 10
                        start_x = end_x - var_width
                        print(f"调整{varnam}到左侧，避免超出页面")
                    
                    var_rect = fitz.Rect(
                        start_x,
                        rect.y0 - 2,
                        end_x,
                        rect.y0 + 16
                    )
                    
                    print(f"{varnam}自动适配: 起始{start_x:.1f}, 结束{end_x:.1f}, 宽度{var_width:.1f}")
                
                # 添加变量注释
                try:
                    text_annot = page.add_freetext_annot(
                        var_rect,
                        varnam,
                        fontsize=var_fontsize,
                        text_color=(0, 0, 0),
                        fill_color=domain_color
                    )
                    text_annot.set_border(width=1, style="solid")
                    text_annot.update()
                    
                    print(f"已添加变量注释: {varnam} for '{pdf_field_name}' at {var_rect}")
                    
                except Exception as e:
                    print(f"变量注释失败 {varnam}: {e}")
        
        # 保存自动适配宽度版本
        doc.save(output_path)
        doc.close()
        
        print(f"\n自动适配宽度版完成!")
        print(f"输出文件: {output_path}")
        
        return True
        
    except Exception as e:
        print(f"注释失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_text_width_calculation():
    """测试文字宽度计算"""
    print("测试文字宽度计算:")
    test_texts = ["AGEU", "BRTHDTC", "AGE", "SEX", "DM(人口学资料)"]
    
    for text in test_texts:
        width_9 = calculate_text_width(text, 9)
        width_10 = calculate_text_width(text, 10)
        print(f"'{text}': 9号字体={width_9:.1f}px, 10号字体={width_10:.1f}px")

if __name__ == "__main__":
    print("自动适配宽度版PDF注释脚本")
    print("="*60)
    
    print("自动适配特性:")
    print("1. 根据文字长度精确计算颜色框宽度")
    print("2. 区分英文和中文字符宽度")
    print("3. 自动添加适当边距")
    print("4. 确保不超出页面边界")
    
    # 测试文字宽度计算
    print("\n测试文字宽度计算...")
    test_text_width_calculation()
    
    # 检查PDF文件
    pdf_path = "HY1005-2023-2-P1_Unique eCRF_V3.0.pdf"
    if not os.path.exists(pdf_path):
        print(f"错误: PDF文件不存在 - {pdf_path}")
        exit(1)
    
    print("\n执行自动适配宽度注释...")
    success = annotate_pdf_auto_fit_width()
    
    if success:
        print("\n自动适配宽度版完成!")
        print("适配结果:")
        print("1. 每个颜色框都精确匹配文字长度")
        print("2. AGEU颜色框刚好容纳'AGEU'四个字符")
        print("3. 不浪费空间，也不会太紧")
        print("4. 确保不超出页面边界")
        print("\n自动适配文件: HY1005-2023-2-P1_Unique eCRF_V3.0_AUTO_FIT_WIDTH_annotated.pdf")
    else:
        print("\n注释失败，请检查错误信息")
