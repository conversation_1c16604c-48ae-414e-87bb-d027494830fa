import pandas as pd
import os
import webbrowser
from datetime import datetime

def create_pdf_annotation_guide():
    """创建PDF注释指南 - 通过HTML转PDF的方式"""
    
    # 读取Excel数据
    try:
        df = pd.read_excel('dm_spec.xlsx')
        print("成功读取Excel数据")
    except Exception as e:
        print(f"读取Excel失败: {e}")
        return
    
    # 获取注释信息
    domain = df['DOMAIN'].iloc[0] if 'DOMAIN' in df.columns else 'Unknown'
    obsclass = df['OBSCLASS'].iloc[0] if 'OBSCLASS' in df.columns else 'Unknown'
    domain_label = f"{domain} ({obsclass})"
    
    # 颜色信息
    color_info = {
        'DM': {'name': 'BLUE', 'rgb': '191, 255, 255', 'hex': '#BFFFFF'},
        'DS': {'name': 'YELLOW', 'rgb': '255, 255, 150', 'hex': '#FFFF96'},
        'SC': {'name': 'GREEN', 'rgb': '150, 255, 150', 'hex': '#96FF96'},
        'VS': {'name': 'ORANGE', 'rgb': '255, 190, 155', 'hex': '#FFBE9B'}
    }.get(domain, {'name': 'BLUE', 'rgb': '191, 255, 255', 'hex': '#BFFFFF'})
    
    # 创建可打印的HTML
    html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>eCRF注释指南 - 可打印版本</title>
    <style>
        @page {{
            size: A4;
            margin: 2cm;
        }}
        body {{
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
        }}
        .header {{
            text-align: center;
            border-bottom: 3px solid #333;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }}
        .header h1 {{
            font-size: 24px;
            margin: 0;
            color: #2c3e50;
        }}
        .section {{
            margin: 25px 0;
            page-break-inside: avoid;
        }}
        .section h2 {{
            font-size: 16px;
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 5px;
            margin-bottom: 15px;
        }}
        .domain-box {{
            background-color: {color_info['hex']};
            border: 3px solid #000;
            padding: 15px 20px;
            margin: 15px 0;
            display: inline-block;
            font-weight: bold;
            font-size: 18px;
            border-radius: 5px;
        }}
        .variable-box {{
            background-color: {color_info['hex']};
            border: 2px solid #000;
            padding: 8px 12px;
            margin: 5px;
            display: inline-block;
            font-size: 12px;
            border-radius: 3px;
            font-weight: bold;
        }}
        .spec-table {{
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }}
        .spec-table th, .spec-table td {{
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }}
        .spec-table th {{
            background-color: #f2f2f2;
            font-weight: bold;
        }}
        .info-box {{
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            margin: 15px 0;
            border-radius: 5px;
        }}
        .step-list {{
            background-color: #e8f4fd;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }}
        .step-list ol {{
            margin: 0;
            padding-left: 20px;
        }}
        .step-list li {{
            margin: 8px 0;
        }}
        .page-break {{
            page-break-before: always;
        }}
        .no-break {{
            page-break-inside: avoid;
        }}
    </style>
</head>
<body>
    <div class="header">
        <h1>eCRF注释实施指南</h1>
        <p>基于SDTM规范的电子病例报告表注释标准</p>
        <p>生成时间: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}</p>
        <p>源文件: dm_spec.xlsx</p>
    </div>

    <div class="section">
        <h2>1. 基本信息</h2>
        <div class="info-box">
            <p><strong>Domain:</strong> {domain}</p>
            <p><strong>OBSCLASS:</strong> {obsclass}</p>
            <p><strong>Domain标签:</strong> {domain_label}</p>
            <p><strong>标准颜色:</strong> {color_info['name']} (RGB: {color_info['rgb']})</p>
            <p><strong>变量数量:</strong> {len(df)}</p>
        </div>
    </div>

    <div class="section">
        <h2>2. Domain标识框样式</h2>
        <p>在eCRF表单顶部添加以下样式的Domain标识框：</p>
        
        <div style="text-align: center; margin: 20px 0;">
            <div class="domain-box">{domain_label}</div>
        </div>
        
        <table class="spec-table">
            <tr><th>属性</th><th>值</th><th>说明</th></tr>
            <tr><td>背景颜色</td><td>{color_info['hex']}</td><td>RGB: {color_info['rgb']}</td></tr>
            <tr><td>边框</td><td>3px 黑色实线</td><td>清晰的边界</td></tr>
            <tr><td>字体</td><td>粗体 18px</td><td>确保可读性</td></tr>
            <tr><td>内边距</td><td>15px 20px</td><td>适当的内部空间</td></tr>
            <tr><td>位置</td><td>表单顶部</td><td>显著位置</td></tr>
        </table>
    </div>

    <div class="section">
        <h2>3. 变量注释框样式</h2>
        <p>在每个数据字段旁边添加对应的变量注释框：</p>
        
        <div style="margin: 20px 0;">
"""
    
    # 添加变量框
    for _, row in df.iterrows():
        varnam = row.get('VARNAM', 'N/A')
        html_content += f'            <div class="variable-box">{varnam}</div>\n'
    
    html_content += f"""
        </div>
        
        <table class="spec-table">
            <tr><th>属性</th><th>值</th><th>说明</th></tr>
            <tr><td>背景颜色</td><td>{color_info['hex']}</td><td>与Domain框相同</td></tr>
            <tr><td>边框</td><td>2px 黑色实线</td><td>清晰但不突兀</td></tr>
            <tr><td>字体</td><td>粗体 12px</td><td>清晰可读</td></tr>
            <tr><td>内边距</td><td>8px 12px</td><td>紧凑布局</td></tr>
            <tr><td>位置</td><td>字段旁边</td><td>就近原则</td></tr>
        </table>
    </div>

    <div class="section page-break">
        <h2>4. 实施步骤</h2>
        <div class="step-list">
            <ol>
                <li><strong>准备工作</strong>
                    <ul>
                        <li>安装PDF编辑器 (推荐Adobe Acrobat或Foxit PDF Editor)</li>
                        <li>准备原始eCRF文件: HY1005-2023-2-P1_Unique eCRF_V3.0.pdf</li>
                    </ul>
                </li>
                <li><strong>添加Domain标识</strong>
                    <ul>
                        <li>在表单顶部添加Domain标识框</li>
                        <li>文本内容: {domain_label}</li>
                        <li>使用{color_info['name']}背景色 (RGB: {color_info['rgb']})</li>
                        <li>设置3px黑色边框</li>
                    </ul>
                </li>
                <li><strong>添加变量注释</strong>
                    <ul>
                        <li>在每个相关数据字段旁边添加变量注释框</li>
                        <li>使用与Domain框相同的背景色</li>
                        <li>设置2px黑色边框</li>
                        <li>确保注释不遮挡原有内容</li>
                    </ul>
                </li>
                <li><strong>质量检查</strong>
                    <ul>
                        <li>检查所有注释的颜色一致性</li>
                        <li>确认注释位置合理</li>
                        <li>验证文字清晰可读</li>
                    </ul>
                </li>
                <li><strong>保存文件</strong>
                    <ul>
                        <li>另存为新的PDF文件</li>
                        <li>建议命名为: [原文件名]_annotated.pdf</li>
                    </ul>
                </li>
            </ol>
        </div>
    </div>

    <div class="section">
        <h2>5. 变量详细信息</h2>
        <table class="spec-table">
            <tr>
                <th>序号</th>
                <th>变量名 (VARNAM)</th>
                <th>变量标签 (VARLABEL)</th>
                <th>注释位置建议</th>
            </tr>
"""
    
    # 添加变量详细信息
    for i, (_, row) in enumerate(df.iterrows(), 1):
        varnam = row.get('VARNAM', 'N/A')
        varlabel = row.get('VARLABEL', 'N/A')
        html_content += f"""
            <tr>
                <td>{i}</td>
                <td><strong>{varnam}</strong></td>
                <td>{varlabel}</td>
                <td>{varlabel}字段旁边</td>
            </tr>
"""
    
    html_content += f"""
        </table>
    </div>

    <div class="section">
        <h2>6. 技术规格总结</h2>
        <div class="info-box">
            <h4>Domain标识框:</h4>
            <ul>
                <li>文本: {domain_label}</li>
                <li>背景: {color_info['hex']} (RGB: {color_info['rgb']})</li>
                <li>边框: 3px solid #000000</li>
                <li>字体: bold 18px</li>
                <li>位置: 表单顶部</li>
            </ul>
            
            <h4>变量注释框:</h4>
            <ul>
                <li>背景: {color_info['hex']} (与Domain框相同)</li>
                <li>边框: 2px solid #000000</li>
                <li>字体: bold 12px</li>
                <li>位置: 相应字段旁边</li>
            </ul>
        </div>
    </div>

    <div class="section">
        <h2>7. 注意事项</h2>
        <div class="info-box">
            <ul>
                <li><strong>颜色一致性:</strong> 确保同一Domain的所有注释使用相同颜色</li>
                <li><strong>位置合理:</strong> 注释应放在不遮挡原有内容的位置</li>
                <li><strong>字体清晰:</strong> 使用黑色字体确保在彩色背景上清晰可读</li>
                <li><strong>边框规范:</strong> Domain框使用3px边框，变量框使用2px边框</li>
                <li><strong>标准遵循:</strong> 严格按照CDISC SDTM标准执行</li>
            </ul>
        </div>
    </div>

    <div class="section">
        <p style="text-align: center; margin-top: 40px; font-style: italic;">
            --- 本指南由eCRF注释生成器自动生成 ---<br>
            如需打印为PDF，请使用浏览器的"打印"功能，选择"另存为PDF"
        </p>
    </div>

</body>
</html>
"""
    
    # 保存HTML文件
    output_file = "ecrf_annotation_printable_guide.html"
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print(f"可打印的HTML指南已生成: {output_file}")
    print("\n如何生成PDF:")
    print("1. 打开生成的HTML文件")
    print("2. 使用浏览器的打印功能 (Ctrl+P)")
    print("3. 选择'另存为PDF'或'打印到PDF'")
    print("4. 调整页面设置确保最佳效果")
    
    return output_file

def main():
    """主函数"""
    print("eCRF注释PDF指南生成器")
    print("="*50)
    
    if not os.path.exists('dm_spec.xlsx'):
        print("错误: dm_spec.xlsx 文件不存在")
        return
    
    # 生成可打印的HTML指南
    html_file = create_pdf_annotation_guide()
    
    if html_file:
        print(f"\n成功生成可打印指南: {html_file}")
        
        # 询问是否打开文件
        try:
            choice = input("\n是否现在打开HTML文件? (y/n): ").lower().strip()
            if choice in ['y', 'yes', '是']:
                webbrowser.open(f'file:///{os.path.abspath(html_file)}')
                print("已在浏览器中打开文件")
                print("\n提示: 在浏览器中按 Ctrl+P 可以打印为PDF")
        except:
            print("已生成文件，请手动打开查看")

if __name__ == "__main__":
    main()
