#!/usr/bin/env python3
"""
简单策略版PDF注释脚本
用直观简单的方法处理AGEU位置
"""

import fitz  # PyMuPDF
import os

def annotate_pdf_simple_strategy():
    """简单策略版注释 - 直接在周岁附近找空位"""
    
    pdf_path = "HY1005-2023-2-P1_Unique eCRF_V3.0.pdf"
    output_path = "HY1005-2023-2-P1_Unique eCRF_V3.0_SIMPLE_STRATEGY_annotated.pdf"
    
    # DM域颜色
    domain_color = (0.75, 1, 1)  # RGB(191,255,255)
    
    # 严格按照Excel数据的字段映射
    field_mappings = {
        "出生日期": "BRTHDTC",
        "年龄": "AGE",
        "周岁": "AGEU",
        "性别": "SEX"
    }
    
    try:
        doc = fitz.open(pdf_path)
        print(f"开始简单策略注释...")
        
        # 只处理第7页
        target_page = 6  # 第7页，索引为6
        page = doc[target_page]
        print(f"\n只处理第 {target_page + 1} 页...")
        
        # 1. 添加Domain标识
        text_instances = page.search_for("人口学资料")
        for rect in text_instances:
            if rect.y0 < page.rect.height * 0.3:
                print(f"找到表单标题 '人口学资料' at {rect}")
                
                domain_rect = fitz.Rect(
                    rect.x0,
                    rect.y0 - 25,
                    rect.x0 + 120,
                    rect.y0 - 5
                )
                
                try:
                    text_annot = page.add_freetext_annot(
                        domain_rect,
                        "DM(人口学资料)",
                        fontsize=10,
                        text_color=(0, 0, 0),
                        fill_color=domain_color
                    )
                    text_annot.set_border(width=2, style="solid")
                    text_annot.update()
                    
                    print(f"已添加Domain标识: DM(人口学资料) at {domain_rect}")
                    break
                    
                except Exception as e:
                    print(f"Domain注释失败: {e}")
                    break
        
        # 2. 添加变量注释 - 用简单直观的方法处理AGEU
        for pdf_field_name, varnam in field_mappings.items():
            text_instances = page.search_for(pdf_field_name)
            if text_instances:
                rect = text_instances[0]
                print(f"找到字段 '{pdf_field_name}' at {rect}")
                
                if pdf_field_name == "周岁":
                    print(f"AGEU简单策略处理...")
                    print(f"'周岁'位置: {rect}")
                    
                    # 简单策略：直接尝试几个紧邻位置
                    candidate_positions = [
                        # 位置1: 上方紧邻
                        ("上方", fitz.Rect(rect.x0, rect.y0 - 18, rect.x0 + 45, rect.y0 - 3)),
                        # 位置2: 下方紧邻  
                        ("下方", fitz.Rect(rect.x0, rect.y1 + 3, rect.x0 + 45, rect.y1 + 18)),
                        # 位置3: 左侧紧邻
                        ("左侧", fitz.Rect(rect.x0 - 50, rect.y0, rect.x0 - 5, rect.y0 + 15)),
                        # 位置4: 右侧紧邻（如果空间够）
                        ("右侧", fitz.Rect(rect.x1 + 5, rect.y0, rect.x1 + 50, rect.y0 + 15))
                    ]
                    
                    # 简单检测：只检查很小的区域是否有文字
                    best_position = None
                    for pos_name, pos_rect in candidate_positions:
                        # 检查是否在页面内
                        if (pos_rect.x0 >= 10 and pos_rect.y0 >= 10 and 
                            pos_rect.x1 <= page.rect.width - 10 and pos_rect.y1 <= page.rect.height - 10):
                            
                            # 简单检测：只检查注释框本身的小区域
                            small_check = fitz.Rect(
                                pos_rect.x0, 
                                pos_rect.y0, 
                                pos_rect.x1, 
                                pos_rect.y1
                            )
                            
                            # 获取这个小区域的文字
                            text_in_area = page.get_textbox(small_check)
                            
                            print(f"  {pos_name}位置 {pos_rect}: 内容='{text_in_area.strip()}'")
                            
                            # 如果这个小区域基本没有文字，就选择这个位置
                            if len(text_in_area.strip()) <= 2:  # 允许1-2个字符的容错
                                print(f"  选择{pos_name}位置: 基本空白!")
                                best_position = pos_rect
                                break
                        else:
                            print(f"  {pos_name}位置: 超出页面边界")
                    
                    if best_position:
                        var_rect = best_position
                        print(f"AGEU最终位置: {var_rect}")
                    else:
                        # 如果都不行，使用最保守的上方位置
                        var_rect = fitz.Rect(rect.x0, rect.y0 - 15, rect.x0 + 45, rect.y0 - 2)
                        print(f"使用保守上方位置: {var_rect}")
                
                else:
                    # 其他字段的常规处理
                    var_rect = fitz.Rect(
                        rect.x1 + 10,
                        rect.y0 - 2,
                        rect.x1 + 70,
                        rect.y0 + 16
                    )
                    
                    # 边界检查
                    if var_rect.x1 > page.rect.width - 10:
                        var_rect = fitz.Rect(
                            rect.x0 - 70,
                            rect.y0 - 2,
                            rect.x0 - 10,
                            rect.y0 + 16
                        )
                
                # 添加变量注释
                try:
                    text_annot = page.add_freetext_annot(
                        var_rect,
                        varnam,
                        fontsize=9,
                        text_color=(0, 0, 0),
                        fill_color=domain_color
                    )
                    text_annot.set_border(width=1, style="solid")
                    text_annot.update()
                    
                    print(f"已添加变量注释: {varnam} for '{pdf_field_name}' at {var_rect}")
                    
                except Exception as e:
                    print(f"变量注释失败 {varnam}: {e}")
        
        # 保存简单策略版本
        doc.save(output_path)
        doc.close()
        
        print(f"\n简单策略版完成!")
        print(f"输出文件: {output_path}")
        
        return True
        
    except Exception as e:
        print(f"注释失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("简单策略版PDF注释脚本")
    print("="*60)
    
    print("新策略:")
    print("1. 不用复杂的大范围检测")
    print("2. 直接在'周岁'紧邻位置找空位")
    print("3. 只检查注释框本身的小区域")
    print("4. 优先顺序: 上方 -> 下方 -> 左侧 -> 右侧")
    
    # 检查PDF文件
    pdf_path = "HY1005-2023-2-P1_Unique eCRF_V3.0.pdf"
    if not os.path.exists(pdf_path):
        print(f"错误: PDF文件不存在 - {pdf_path}")
        exit(1)
    
    print("\n执行简单策略注释...")
    success = annotate_pdf_simple_strategy()
    
    if success:
        print("\n简单策略版完成!")
        print("策略改进:")
        print("1. 用直观的紧邻位置检测")
        print("2. 避免复杂的大范围文本分析")
        print("3. 优先选择距离最近的空位")
        print("\n简单策略文件: HY1005-2023-2-P1_Unique eCRF_V3.0_SIMPLE_STRATEGY_annotated.pdf")
    else:
        print("\n注释失败，请检查错误信息")
