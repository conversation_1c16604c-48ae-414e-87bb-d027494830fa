import pdfplumber
import pandas as pd
import re

def analyze_pdf_structure(pdf_path):
    """分析PDF文件结构"""
    print(f"正在分析PDF文件: {pdf_path}")
    
    with pdfplumber.open(pdf_path) as pdf:
        print(f"PDF总页数: {len(pdf.pages)}")
        
        # 分析前几页的内容
        for i, page in enumerate(pdf.pages[:5]):  # 只看前5页
            print(f"\n=== 第 {i+1} 页 ===")
            text = page.extract_text()
            if text:
                lines = text.split('\n')[:10]  # 只显示前10行
                for j, line in enumerate(lines):
                    if line.strip():
                        print(f"  {j+1}: {line.strip()}")
            else:
                print("  (无文本内容)")

def read_excel_data():
    """读取Excel数据"""
    df = pd.read_excel('dm_spec.xlsx')
    print("\nExcel数据内容:")
    print(f"Domain: {df['DOMAIN'].iloc[0]}")
    print(f"OBSCLASS: {df['OBSCLASS'].iloc[0]}")
    
    print("\n变量信息:")
    for index, row in df.iterrows():
        print(f"  {row['VARNAM']}: {row['VARLABEL']}")
    
    return df

if __name__ == "__main__":
    # 分析PDF文件
    pdf_path = "HY1005-2023-2-P1_Unique eCRF_V3.0.pdf"
    analyze_pdf_structure(pdf_path)
    
    # 读取Excel数据
    excel_data = read_excel_data()
