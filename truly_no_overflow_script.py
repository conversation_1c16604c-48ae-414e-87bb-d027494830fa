#!/usr/bin/env python3
"""
真正无溢出版PDF注释脚本
大幅增加安全边距，确保AGEU颜色框绝对不出界
"""

import fitz  # PyMuPDF
import os

def annotate_pdf_truly_no_overflow():
    """真正无溢出版注释 - 大幅增加安全边距"""
    
    pdf_path = "HY1005-2023-2-P1_Unique eCRF_V3.0.pdf"
    output_path = "HY1005-2023-2-P1_Unique eCRF_V3.0_TRULY_NO_OVERFLOW_annotated.pdf"
    
    # DM域颜色
    domain_color = (0.75, 1, 1)  # RGB(191,255,255)
    
    # 严格按照Excel数据的字段映射
    field_mappings = {
        "出生日期": "BRTHDTC",
        "年龄": "AGE",
        "周岁": "AGEU",
        "性别": "SEX"
    }
    
    try:
        doc = fitz.open(pdf_path)
        print(f"开始真正无溢出注释...")
        
        # 只处理第7页
        target_page = 6  # 第7页，索引为6
        page = doc[target_page]
        print(f"\n只处理第 {target_page + 1} 页...")
        print(f"页面宽度: {page.rect.width}")
        
        # 大幅增加安全边距
        SAFE_MARGIN = 50  # 距离页面边缘至少50像素
        print(f"安全边距: {SAFE_MARGIN} 像素")
        
        # 1. 添加Domain标识
        text_instances = page.search_for("人口学资料")
        for rect in text_instances:
            if rect.y0 < page.rect.height * 0.3:
                print(f"找到表单标题 '人口学资料' at {rect}")
                
                domain_rect = fitz.Rect(
                    rect.x0,
                    rect.y0 - 25,
                    rect.x0 + 120,
                    rect.y0 - 5
                )
                
                try:
                    text_annot = page.add_freetext_annot(
                        domain_rect,
                        "DM(人口学资料)",
                        fontsize=10,
                        text_color=(0, 0, 0),
                        fill_color=domain_color
                    )
                    text_annot.set_border(width=2, style="solid")
                    text_annot.update()
                    
                    print(f"已添加Domain标识: DM(人口学资料) at {domain_rect}")
                    break
                    
                except Exception as e:
                    print(f"Domain注释失败: {e}")
                    break
        
        # 2. 添加变量注释 - 特别处理AGEU
        for pdf_field_name, varnam in field_mappings.items():
            text_instances = page.search_for(pdf_field_name)
            if text_instances:
                rect = text_instances[0]
                print(f"找到字段 '{pdf_field_name}' at {rect}")
                
                if pdf_field_name == "周岁":
                    print(f"AGEU特殊处理 - 大幅缩小颜色框...")
                    print(f"'周岁'位置: {rect}")
                    
                    # 大幅缩小AGEU颜色框宽度
                    ageu_width = 30  # 固定30像素宽度，确保足够小
                    max_right_boundary = page.rect.width - SAFE_MARGIN  # 545像素
                    
                    print(f"AGEU固定宽度: {ageu_width} 像素")
                    print(f"最大右边界: {max_right_boundary} 像素")
                    
                    # 计算起始位置，确保不超出边界
                    start_x = rect.x0  # 从"周岁"左边开始
                    end_x = start_x + ageu_width
                    
                    if end_x > max_right_boundary:
                        # 如果超出，从右边界往左计算
                        end_x = max_right_boundary
                        start_x = end_x - ageu_width
                        print(f"调整AGEU位置，从右边界往左计算")
                    
                    var_rect = fitz.Rect(
                        start_x,
                        rect.y0 - 18,  # 在"周岁"上方
                        end_x,
                        rect.y0 - 3
                    )
                    
                    print(f"AGEU最终位置: {var_rect}")
                    print(f"起始位置: {start_x:.1f}")
                    print(f"结束位置: {end_x:.1f}")
                    print(f"颜色框宽度: {end_x - start_x:.1f}")
                    print(f"距离页面右边缘: {page.rect.width - end_x:.1f} 像素")
                    
                    # 验证是否真的不出界
                    if end_x > page.rect.width - SAFE_MARGIN:
                        print(f"警告: 仍然可能出界!")
                    else:
                        print(f"确认: 绝对不出界!")
                
                else:
                    # 其他字段的处理
                    text_length = len(varnam)
                    estimated_width = text_length * 8 + 10
                    
                    var_rect = fitz.Rect(
                        rect.x1 + 10,
                        rect.y0 - 2,
                        rect.x1 + 10 + estimated_width,
                        rect.y0 + 16
                    )
                    
                    # 边界检查
                    if var_rect.x1 > page.rect.width - SAFE_MARGIN:
                        # 调整到左侧
                        var_rect = fitz.Rect(
                            rect.x0 - 10 - estimated_width,
                            rect.y0 - 2,
                            rect.x0 - 10,
                            rect.y0 + 16
                        )
                        print(f"调整{varnam}到左侧，避免超出页面")
                    
                    print(f"{varnam}位置: {var_rect}, 宽度: {var_rect.x1 - var_rect.x0:.1f}")
                
                # 添加变量注释
                try:
                    text_annot = page.add_freetext_annot(
                        var_rect,
                        varnam,
                        fontsize=9,
                        text_color=(0, 0, 0),
                        fill_color=domain_color
                    )
                    text_annot.set_border(width=1, style="solid")
                    text_annot.update()
                    
                    print(f"已添加变量注释: {varnam} for '{pdf_field_name}' at {var_rect}")
                    
                except Exception as e:
                    print(f"变量注释失败 {varnam}: {e}")
        
        # 保存真正无溢出版本
        doc.save(output_path)
        doc.close()
        
        print(f"\n真正无溢出版完成!")
        print(f"输出文件: {output_path}")
        
        return True
        
    except Exception as e:
        print(f"注释失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("真正无溢出版PDF注释脚本")
    print("="*60)
    
    print("激进的溢出控制:")
    print("1. 安全边距增加到50像素")
    print("2. AGEU颜色框固定30像素宽度")
    print("3. 确保右边界不超过545像素")
    print("4. 绝对保证不出界")
    
    # 检查PDF文件
    pdf_path = "HY1005-2023-2-P1_Unique eCRF_V3.0.pdf"
    if not os.path.exists(pdf_path):
        print(f"错误: PDF文件不存在 - {pdf_path}")
        exit(1)
    
    print("\n执行真正无溢出注释...")
    success = annotate_pdf_truly_no_overflow()
    
    if success:
        print("\n真正无溢出版完成!")
        print("激进控制结果:")
        print("1. AGEU颜色框只有30像素宽")
        print("2. 距离右边缘至少50像素")
        print("3. 绝对保证不出界")
        print("4. 保持在'周岁'上方位置")
        print("\n真正无溢出文件: HY1005-2023-2-P1_Unique eCRF_V3.0_TRULY_NO_OVERFLOW_annotated.pdf")
    else:
        print("\n注释失败，请检查错误信息")
