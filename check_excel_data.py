import pandas as pd

# 读取Excel数据
df = pd.read_excel('dm_spec.xlsx')
print('Excel中的实际数据:')
print('列名:', list(df.columns))
print()

for i, row in df.iterrows():
    print(f'行{i+1}:')
    print(f'  DOMAIN: {row.get("DOMAIN", "N/A")}')
    print(f'  OBSCLASS: {row.get("OBSCLASS", "N/A")}')
    print(f'  VARNAM: {row.get("VARNAM", "N/A")}')
    print(f'  VARLABEL: {row.get("VARLABEL", "N/A")}')
    print()

print("="*50)
print("根据用户截图，PDF中的实际字段名称:")
print("- 出生日期")
print("- 年龄") 
print("- 性别")
print("- 民族")
print("- 其他民族")
print("- 身高")
print("- 体重")
print("- BMI")
