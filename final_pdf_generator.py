import pandas as pd
import os
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import A4, letter
from reportlab.lib.colors import Color
from reportlab.lib.units import mm
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
import PyPDF2
from io import BytesIO

class FinalPDFGenerator:
    """最终PDF注释生成器"""
    
    def __init__(self, excel_path, original_pdf_path=None):
        self.excel_path = excel_path
        self.original_pdf_path = original_pdf_path
        self.excel_data = None
        self.annotation_data = None
        
        # Domain颜色定义
        self.domain_colors = {
            'DM': Color(191/255, 1.0, 1.0, alpha=0.7),  # 蓝色
            'DS': Color(1.0, 1.0, 150/255, alpha=0.7),   # 黄色
            'SC': Color(150/255, 1.0, 150/255, alpha=0.7), # 绿色
            'VS': Color(1.0, 190/255, 155/255, alpha=0.7)  # 橙色
        }
    
    def read_excel_data(self):
        """读取Excel数据"""
        try:
            self.excel_data = pd.read_excel(self.excel_path)
            print(f"成功读取Excel文件: {self.excel_path}")
            return True
        except Exception as e:
            print(f"读取Excel文件失败: {e}")
            return False
    
    def prepare_annotation_data(self):
        """准备注释数据"""
        if self.excel_data is None:
            return False
        
        domain = self.excel_data['DOMAIN'].iloc[0] if 'DOMAIN' in self.excel_data.columns else 'DM'
        obsclass = self.excel_data['OBSCLASS'].iloc[0] if 'OBSCLASS' in self.excel_data.columns else '人口学资料'
        
        self.annotation_data = {
            'domain': domain,
            'obsclass': obsclass,
            'domain_label': f"{domain} ({obsclass})",
            'color': self.domain_colors.get(domain, self.domain_colors['DM']),
            'variables': []
        }
        
        for _, row in self.excel_data.iterrows():
            var_info = {
                'varnam': str(row.get('VARNAM', 'N/A')),
                'varlabel': str(row.get('VARLABEL', 'N/A')),
                'order': row.get('ORDER', 0)
            }
            self.annotation_data['variables'].append(var_info)
        
        return True
    
    def create_annotation_pdf(self, output_path):
        """创建注释PDF文件"""
        try:
            # 创建PDF画布
            c = canvas.Canvas(output_path, pagesize=A4)
            width, height = A4
            
            # 设置字体
            c.setFont("Helvetica-Bold", 16)
            
            # 页面标题
            c.drawString(50, height - 50, "eCRF注释示例 - 基于SDTM规范")
            
            # 基本信息
            c.setFont("Helvetica", 12)
            info_y = height - 90
            c.drawString(50, info_y, f"源文件: {self.excel_path}")
            c.drawString(50, info_y - 20, f"Domain: {self.annotation_data['domain']}")
            c.drawString(50, info_y - 40, f"OBSCLASS: {self.annotation_data['obsclass']}")
            
            # Domain标识框区域
            domain_y = height - 200
            c.setFont("Helvetica-Bold", 14)
            c.drawString(50, domain_y + 50, "1. Domain标识框 (放置在eCRF表单顶部):")
            
            # 绘制Domain标识框
            domain_color = self.annotation_data['color']
            
            # Domain框
            box_width = 300
            box_height = 50
            
            c.setFillColor(domain_color)
            c.setStrokeColor('black')
            c.setLineWidth(3)
            c.rect(50, domain_y, box_width, box_height, fill=1, stroke=1)
            
            # Domain文本
            c.setFillColor('black')
            c.setFont("Helvetica-Bold", 16)
            text_x = 50 + 15
            text_y = domain_y + 20
            c.drawString(text_x, text_y, self.annotation_data['domain_label'])
            
            # RGB值标注
            c.setFont("Helvetica", 10)
            rgb_text = f"RGB: 191, 255, 255"
            c.drawString(50 + box_width + 20, domain_y + 25, rgb_text)
            
            # 变量注释区域
            var_y = domain_y - 100
            c.setFont("Helvetica-Bold", 14)
            c.drawString(50, var_y + 30, "2. 变量注释框 (放置在相应字段旁边):")
            
            # 绘制变量注释框
            var_box_width = 120
            var_box_height = 35
            vars_per_row = 3
            
            for i, var in enumerate(self.annotation_data['variables']):
                row = i // vars_per_row
                col = i % vars_per_row
                
                box_x = 50 + col * 150
                box_y = var_y - row * 80
                
                # 检查是否需要新页面
                if box_y < 150:
                    c.showPage()
                    c.setFont("Helvetica-Bold", 14)
                    var_y = height - 50
                    box_y = var_y - row * 80
                
                # 绘制变量框
                c.setFillColor(domain_color)
                c.setStrokeColor('black')
                c.setLineWidth(2)
                c.rect(box_x, box_y, var_box_width, var_box_height, fill=1, stroke=1)
                
                # 变量名
                c.setFillColor('black')
                c.setFont("Helvetica-Bold", 12)
                c.drawString(box_x + 8, box_y + 18, var['varnam'])
                
                # 变量标签（在框下方）
                c.setFont("Helvetica", 9)
                label_text = var['varlabel']
                if len(label_text) > 15:
                    label_text = label_text[:12] + "..."
                c.drawString(box_x, box_y - 15, label_text)
                
                # 完整标签（在框下方第二行）
                c.setFont("Helvetica", 8)
                c.drawString(box_x, box_y - 28, var['varlabel'])
            
            # 新页面 - 实施说明
            c.showPage()
            c.setFont("Helvetica-Bold", 18)
            c.drawString(50, height - 50, "实施说明")
            
            # 实施步骤
            steps_y = height - 100
            c.setFont("Helvetica-Bold", 14)
            c.drawString(50, steps_y, "如何在原始eCRF上添加这些注释:")
            
            steps = [
                "1. 使用PDF编辑器打开原始eCRF文件",
                "2. 在表单顶部添加Domain标识框",
                "   - 文本: " + self.annotation_data['domain_label'],
                "   - 背景色: 蓝色 (RGB: 191, 255, 255)",
                "   - 边框: 3px 黑色实线",
                "",
                "3. 在每个相关字段旁边添加变量注释框:",
            ]
            
            step_y = steps_y - 40
            c.setFont("Helvetica", 12)
            for step in steps:
                c.drawString(70, step_y, step)
                step_y -= 20
            
            # 变量列表
            for i, var in enumerate(self.annotation_data['variables'], 1):
                if step_y < 100:
                    c.showPage()
                    step_y = height - 50
                
                c.drawString(90, step_y, f"• {var['varnam']} → {var['varlabel']}")
                step_y -= 18
            
            # 技术规格
            step_y -= 30
            c.setFont("Helvetica-Bold", 12)
            c.drawString(70, step_y, "技术规格:")
            
            specs = [
                "• 变量框背景色: 与Domain框相同",
                "• 变量框边框: 2px 黑色实线",
                "• 字体: 黑色粗体",
                "• 确保注释不遮挡原有内容"
            ]
            
            step_y -= 25
            c.setFont("Helvetica", 11)
            for spec in specs:
                c.drawString(90, step_y, spec)
                step_y -= 18
            
            # 保存PDF
            c.save()
            print(f"注释PDF已创建: {output_path}")
            return True
            
        except Exception as e:
            print(f"创建PDF失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def create_overlay_pdf(self, output_path):
        """创建覆盖层PDF（用于与原始PDF合并）"""
        try:
            # 创建内存中的PDF
            buffer = BytesIO()
            c = canvas.Canvas(buffer, pagesize=A4)
            width, height = A4
            
            # 在页面顶部添加Domain标识框
            domain_color = self.annotation_data['color']
            
            # Domain框位置（页面顶部）
            domain_x = 50
            domain_y = height - 100
            domain_width = 250
            domain_height = 40
            
            c.setFillColor(domain_color)
            c.setStrokeColor('black')
            c.setLineWidth(3)
            c.rect(domain_x, domain_y, domain_width, domain_height, fill=1, stroke=1)
            
            # Domain文本
            c.setFillColor('black')
            c.setFont("Helvetica-Bold", 14)
            c.drawString(domain_x + 10, domain_y + 15, self.annotation_data['domain_label'])
            
            # 在页面中部添加一些变量注释框（示例位置）
            var_positions = [
                (100, height - 200),  # 第一个变量位置
                (300, height - 200),  # 第二个变量位置
                (100, height - 300),  # 第三个变量位置
                (300, height - 300),  # 第四个变量位置
            ]
            
            for i, var in enumerate(self.annotation_data['variables']):
                if i < len(var_positions):
                    x, y = var_positions[i]
                    
                    # 变量框
                    c.setFillColor(domain_color)
                    c.setStrokeColor('black')
                    c.setLineWidth(2)
                    c.rect(x, y, 100, 25, fill=1, stroke=1)
                    
                    # 变量名
                    c.setFillColor('black')
                    c.setFont("Helvetica-Bold", 10)
                    c.drawString(x + 5, y + 8, var['varnam'])
            
            c.save()
            
            # 保存到文件
            buffer.seek(0)
            with open(output_path, 'wb') as f:
                f.write(buffer.getvalue())
            
            print(f"覆盖层PDF已创建: {output_path}")
            return True
            
        except Exception as e:
            print(f"创建覆盖层PDF失败: {e}")
            return False

def main():
    """主函数"""
    excel_path = "dm_spec.xlsx"
    original_pdf = "HY1005-2023-2-P1_Unique eCRF_V3.0.pdf"
    
    print("最终PDF注释生成器")
    print("="*50)
    
    # 检查文件
    if not os.path.exists(excel_path):
        print(f"错误: Excel文件不存在 - {excel_path}")
        return
    
    # 创建生成器
    generator = FinalPDFGenerator(excel_path, original_pdf)
    
    # 读取数据
    if not generator.read_excel_data():
        return
    
    # 准备注释数据
    if not generator.prepare_annotation_data():
        print("准备注释数据失败")
        return
    
    # 显示信息
    print(f"\n注释信息:")
    print(f"Domain: {generator.annotation_data['domain']}")
    print(f"OBSCLASS: {generator.annotation_data['obsclass']}")
    print(f"变量数量: {len(generator.annotation_data['variables'])}")
    
    # 生成PDF文件
    print(f"\n正在生成PDF文件...")
    
    # 1. 创建完整的注释示例PDF
    annotation_pdf = "eCRF_Annotation_Guide.pdf"
    if generator.create_annotation_pdf(annotation_pdf):
        print(f"✓ 注释示例PDF: {annotation_pdf}")
    
    # 2. 创建覆盖层PDF
    overlay_pdf = "eCRF_Annotation_Overlay.pdf"
    if generator.create_overlay_pdf(overlay_pdf):
        print(f"✓ 覆盖层PDF: {overlay_pdf}")
    
    print(f"\n生成完成!")
    print(f"文件说明:")
    print(f"1. {annotation_pdf} - 完整的注释示例和实施指南")
    print(f"2. {overlay_pdf} - 可与原始eCRF合并的覆盖层")
    
    if os.path.exists(original_pdf):
        print(f"\n原始eCRF文件已找到: {original_pdf}")
        print(f"您可以使用PDF编辑器将覆盖层与原始文件合并")
    else:
        print(f"\n原始eCRF文件未找到: {original_pdf}")
        print(f"请手动使用PDF编辑器根据示例添加注释")

if __name__ == "__main__":
    main()
