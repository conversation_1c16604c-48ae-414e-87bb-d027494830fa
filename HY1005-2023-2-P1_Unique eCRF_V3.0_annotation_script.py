#!/usr/bin/env python3
"""
自动生成的PDF注释脚本
使用PyMuPDF (fitz) 在PDF上添加精确注释
"""

import fitz  # PyMuPDF
import os

def annotate_pdf():
    """在PDF上添加注释"""
    
    # 文件路径
    pdf_path = "HY1005-2023-2-P1_Unique eCRF_V3.0.pdf"
    output_path = "HY1005-2023-2-P1_Unique eCRF_V3.0_annotated.pdf"
    
    # 颜色定义 (fitz 0-1 浮点格式)
    domain_color = (0.75,1,1)  # DM域颜色
    
    try:
        # 打开PDF文件
        doc = fitz.open(pdf_path)
        print(f"成功打开PDF: {pdf_path}")
        print(f"  总页数: {len(doc)}")
        
        # 搜索关键词
        search_terms = ['人口学资料', 'Demographics', 'DEMOGRAPHICS', '人口学', 'Demography']
        domain_added = False
        
        # 遍历所有页面
        for page_num in range(len(doc)):
            page = doc[page_num]
            print(f"\n处理第 {page_num + 1} 页...")
            
            # 1. 查找并添加Domain标识
            if not domain_added:
                for term in search_terms:
                    text_instances = page.search_for(term)
                    if text_instances:
                        rect = text_instances[0]  # 第一个匹配
                        print(f"找到 '{term}' at {rect}")
                        
                        # 在左上方添加Domain框
                        domain_rect = fitz.Rect(
                            rect.x0 - 10,
                            rect.y0 - 30,
                            rect.x0 + 110,
                            rect.y0 - 5
                        )
                        
                        # 添加彩色矩形
                        annot = page.add_rect_annot(domain_rect)
                        annot.set_colors(stroke=domain_color, fill=domain_color)
                        annot.set_border(width=2)
                        annot.update()
                        
                        # 添加文字
                        text_point = fitz.Point(domain_rect.x0 + 5, domain_rect.y0 + 15)
                        page.insert_text(text_point, "DM(人口学资料)", 
                                       fontsize=10, color=(0, 0, 0))
                        
                        print(f"已添加Domain注释: DM(人口学资料)")
                        domain_added = True
                        break
            
            # 2. 查找并添加变量注释

            # 查找变量: BRTHDTC -> 出生日期/时间
            varlabel = "出生日期/时间"
            varnam = "BRTHDTC"
            
            text_instances = page.search_for(varlabel)
            if text_instances:
                rect = text_instances[0]
                print(f"找到变量 '{varlabel}' at {rect}")
                
                # 在右侧添加变量框
                var_rect = fitz.Rect(
                    rect.x1 + 10,
                    rect.y0,
                    rect.x1 + 90,
                    rect.y0 + 20
                )
                
                # 添加彩色矩形
                annot = page.add_rect_annot(var_rect)
                annot.set_colors(stroke=domain_color, fill=domain_color)
                annot.set_border(width=1)
                annot.update()
                
                # 添加文字
                text_point = fitz.Point(var_rect.x0 + 3, var_rect.y0 + 12)
                page.insert_text(text_point, varnam, fontsize=8, color=(0, 0, 0))
                
                print(f"已添加变量注释: {varnam}")
            else:
                # 尝试部分匹配
                words = varlabel.split()
                for word in words:
                    if len(word) > 2:
                        text_instances = page.search_for(word)
                        if text_instances:
                            rect = text_instances[0]
                            print(f"部分匹配 '{word}' for {varlabel} at {rect}")
                            
                            var_rect = fitz.Rect(rect.x1 + 10, rect.y0, rect.x1 + 90, rect.y0 + 20)
                            annot = page.add_rect_annot(var_rect)
                            annot.set_colors(stroke=domain_color, fill=domain_color)
                            annot.set_border(width=1)
                            annot.update()
                            
                            text_point = fitz.Point(var_rect.x0 + 3, var_rect.y0 + 12)
                            page.insert_text(text_point, varnam, fontsize=8, color=(0, 0, 0))
                            print(f"已添加变量注释: {varnam}")
                            break
                else:
                    print(f"未找到变量: {varlabel}")

            # 查找变量: AGE -> 年龄
            varlabel = "年龄"
            varnam = "AGE"
            
            text_instances = page.search_for(varlabel)
            if text_instances:
                rect = text_instances[0]
                print(f"✓ 找到变量 '{varlabel}' at {rect}")
                
                # 在右侧添加变量框
                var_rect = fitz.Rect(
                    rect.x1 + 10,
                    rect.y0,
                    rect.x1 + 90,
                    rect.y0 + 20
                )
                
                # 添加彩色矩形
                annot = page.add_rect_annot(var_rect)
                annot.set_colors(stroke=domain_color, fill=domain_color)
                annot.set_border(width=1)
                annot.update()
                
                # 添加文字
                text_point = fitz.Point(var_rect.x0 + 3, var_rect.y0 + 12)
                page.insert_text(text_point, varnam, fontsize=8, color=(0, 0, 0))
                
                print(f"✓ 已添加变量注释: {varnam}")
            else:
                # 尝试部分匹配
                words = varlabel.split()
                for word in words:
                    if len(word) > 2:
                        text_instances = page.search_for(word)
                        if text_instances:
                            rect = text_instances[0]
                            print(f"✓ 部分匹配 '{word}' for {varlabel} at {rect}")
                            
                            var_rect = fitz.Rect(rect.x1 + 10, rect.y0, rect.x1 + 90, rect.y0 + 20)
                            annot = page.add_rect_annot(var_rect)
                            annot.set_colors(stroke=domain_color, fill=domain_color)
                            annot.set_border(width=1)
                            annot.update()
                            
                            text_point = fitz.Point(var_rect.x0 + 3, var_rect.y0 + 12)
                            page.insert_text(text_point, varnam, fontsize=8, color=(0, 0, 0))
                            print(f"✓ 已添加变量注释: {varnam}")
                            break
                else:
                    print(f"⚠ 未找到变量: {varlabel}")

            # 查找变量: AGEU -> 年龄单位
            varlabel = "年龄单位"
            varnam = "AGEU"
            
            text_instances = page.search_for(varlabel)
            if text_instances:
                rect = text_instances[0]
                print(f"✓ 找到变量 '{varlabel}' at {rect}")
                
                # 在右侧添加变量框
                var_rect = fitz.Rect(
                    rect.x1 + 10,
                    rect.y0,
                    rect.x1 + 90,
                    rect.y0 + 20
                )
                
                # 添加彩色矩形
                annot = page.add_rect_annot(var_rect)
                annot.set_colors(stroke=domain_color, fill=domain_color)
                annot.set_border(width=1)
                annot.update()
                
                # 添加文字
                text_point = fitz.Point(var_rect.x0 + 3, var_rect.y0 + 12)
                page.insert_text(text_point, varnam, fontsize=8, color=(0, 0, 0))
                
                print(f"✓ 已添加变量注释: {varnam}")
            else:
                # 尝试部分匹配
                words = varlabel.split()
                for word in words:
                    if len(word) > 2:
                        text_instances = page.search_for(word)
                        if text_instances:
                            rect = text_instances[0]
                            print(f"✓ 部分匹配 '{word}' for {varlabel} at {rect}")
                            
                            var_rect = fitz.Rect(rect.x1 + 10, rect.y0, rect.x1 + 90, rect.y0 + 20)
                            annot = page.add_rect_annot(var_rect)
                            annot.set_colors(stroke=domain_color, fill=domain_color)
                            annot.set_border(width=1)
                            annot.update()
                            
                            text_point = fitz.Point(var_rect.x0 + 3, var_rect.y0 + 12)
                            page.insert_text(text_point, varnam, fontsize=8, color=(0, 0, 0))
                            print(f"✓ 已添加变量注释: {varnam}")
                            break
                else:
                    print(f"⚠ 未找到变量: {varlabel}")

            # 查找变量: SEX -> 性别
            varlabel = "性别"
            varnam = "SEX"
            
            text_instances = page.search_for(varlabel)
            if text_instances:
                rect = text_instances[0]
                print(f"✓ 找到变量 '{varlabel}' at {rect}")
                
                # 在右侧添加变量框
                var_rect = fitz.Rect(
                    rect.x1 + 10,
                    rect.y0,
                    rect.x1 + 90,
                    rect.y0 + 20
                )
                
                # 添加彩色矩形
                annot = page.add_rect_annot(var_rect)
                annot.set_colors(stroke=domain_color, fill=domain_color)
                annot.set_border(width=1)
                annot.update()
                
                # 添加文字
                text_point = fitz.Point(var_rect.x0 + 3, var_rect.y0 + 12)
                page.insert_text(text_point, varnam, fontsize=8, color=(0, 0, 0))
                
                print(f"✓ 已添加变量注释: {varnam}")
            else:
                # 尝试部分匹配
                words = varlabel.split()
                for word in words:
                    if len(word) > 2:
                        text_instances = page.search_for(word)
                        if text_instances:
                            rect = text_instances[0]
                            print(f"✓ 部分匹配 '{word}' for {varlabel} at {rect}")
                            
                            var_rect = fitz.Rect(rect.x1 + 10, rect.y0, rect.x1 + 90, rect.y0 + 20)
                            annot = page.add_rect_annot(var_rect)
                            annot.set_colors(stroke=domain_color, fill=domain_color)
                            annot.set_border(width=1)
                            annot.update()
                            
                            text_point = fitz.Point(var_rect.x0 + 3, var_rect.y0 + 12)
                            page.insert_text(text_point, varnam, fontsize=8, color=(0, 0, 0))
                            print(f"✓ 已添加变量注释: {varnam}")
                            break
                else:
                    print(f"⚠ 未找到变量: {varlabel}")

        
        # 保存注释后的PDF
        doc.save(output_path)
        doc.close()
        
        print(f"\n🎉 注释完成!")
        print(f"📁 输出文件: {output_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ 注释失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("PDF注释脚本")
    print("="*50)
    
    # 检查依赖
    try:
        import fitz
        print("✓ PyMuPDF (fitz) 已安装")
    except ImportError:
        print("❌ 请先安装PyMuPDF: pip install pymupdf")
        exit(1)
    
    # 执行注释
    annotate_pdf()
