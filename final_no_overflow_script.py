#!/usr/bin/env python3
"""
最终无溢出版PDF注释脚本
确保颜色框完全不出界
"""

import fitz  # PyMuPDF
import os

def annotate_pdf_no_overflow():
    """最终无溢出版注释 - 确保颜色框完全不出界"""
    
    pdf_path = "HY1005-2023-2-P1_Unique eCRF_V3.0.pdf"
    output_path = "HY1005-2023-2-P1_Unique eCRF_V3.0_NO_OVERFLOW_annotated.pdf"
    
    # DM域颜色
    domain_color = (0.75, 1, 1)  # RGB(191,255,255)
    
    # 严格按照Excel数据的字段映射
    field_mappings = {
        "出生日期": "BRTHDTC",
        "年龄": "AGE",
        "周岁": "AGEU",
        "性别": "SEX"
    }
    
    try:
        doc = fitz.open(pdf_path)
        print(f"开始最终无溢出注释...")
        
        # 只处理第7页
        target_page = 6  # 第7页，索引为6
        page = doc[target_page]
        print(f"\n只处理第 {target_page + 1} 页...")
        print(f"页面宽度: {page.rect.width}")
        print(f"页面高度: {page.rect.height}")
        
        # 设置安全边距
        SAFE_MARGIN = 20  # 距离页面边缘至少20像素
        
        # 1. 添加Domain标识
        text_instances = page.search_for("人口学资料")
        for rect in text_instances:
            if rect.y0 < page.rect.height * 0.3:
                print(f"找到表单标题 '人口学资料' at {rect}")
                
                domain_rect = fitz.Rect(
                    rect.x0,
                    rect.y0 - 25,
                    rect.x0 + 120,
                    rect.y0 - 5
                )
                
                try:
                    text_annot = page.add_freetext_annot(
                        domain_rect,
                        "DM(人口学资料)",
                        fontsize=10,
                        text_color=(0, 0, 0),
                        fill_color=domain_color
                    )
                    text_annot.set_border(width=2, style="solid")
                    text_annot.update()
                    
                    print(f"已添加Domain标识: DM(人口学资料) at {domain_rect}")
                    break
                    
                except Exception as e:
                    print(f"Domain注释失败: {e}")
                    break
        
        # 2. 添加变量注释 - 严格控制边界
        for pdf_field_name, varnam in field_mappings.items():
            text_instances = page.search_for(pdf_field_name)
            if text_instances:
                rect = text_instances[0]
                print(f"找到字段 '{pdf_field_name}' at {rect}")
                
                if pdf_field_name == "周岁":
                    print(f"AGEU处理 - 严格控制边界...")
                    print(f"'周岁'位置: {rect}")
                    
                    # 计算AGEU注释框 - 严格控制不出界
                    text_length = len(varnam)  # "AGEU" = 4个字符
                    estimated_width = text_length * 8 + 10  # 每个字符约8像素，加10像素边距
                    
                    print(f"AGEU文字长度: {text_length} 字符")
                    print(f"估算宽度: {estimated_width} 像素")
                    
                    # 计算可用的最大宽度
                    max_available_width = page.rect.width - SAFE_MARGIN - rect.x0
                    print(f"从'周岁'位置到页面右边缘的可用宽度: {max_available_width}")
                    
                    # 选择较小的宽度
                    actual_width = min(estimated_width, max_available_width)
                    print(f"实际使用宽度: {actual_width}")
                    
                    # 如果宽度不够，调整起始位置
                    if actual_width < estimated_width:
                        print(f"宽度不够，调整起始位置...")
                        # 从右边界往左计算起始位置
                        start_x = page.rect.width - SAFE_MARGIN - estimated_width
                        if start_x < 10:  # 如果太靠左，使用最小起始位置
                            start_x = 10
                            actual_width = page.rect.width - SAFE_MARGIN - start_x
                        else:
                            actual_width = estimated_width
                        
                        var_rect = fitz.Rect(
                            start_x,
                            rect.y0 - 18,
                            start_x + actual_width,
                            rect.y0 - 3
                        )
                        print(f"调整后的AGEU位置: {var_rect}")
                    else:
                        # 正常情况，在"周岁"上方
                        var_rect = fitz.Rect(
                            rect.x0,
                            rect.y0 - 18,
                            rect.x0 + actual_width,
                            rect.y0 - 3
                        )
                        print(f"正常AGEU位置: {var_rect}")
                    
                    # 最终边界检查
                    if var_rect.x1 > page.rect.width - SAFE_MARGIN:
                        print(f"警告: 右边界超出，强制调整...")
                        var_rect = fitz.Rect(
                            var_rect.x0,
                            var_rect.y0,
                            page.rect.width - SAFE_MARGIN,
                            var_rect.y1
                        )
                    
                    print(f"AGEU最终位置: {var_rect}")
                    print(f"颜色框宽度: {var_rect.x1 - var_rect.x0:.1f}")
                    print(f"右边界: {var_rect.x1:.1f}, 页面宽度: {page.rect.width}, 安全边距: {SAFE_MARGIN}")
                    print(f"距离右边缘: {page.rect.width - var_rect.x1:.1f} 像素")
                
                else:
                    # 其他字段的处理 - 也要严格控制边界
                    text_length = len(varnam)
                    estimated_width = text_length * 8 + 10
                    
                    var_rect = fitz.Rect(
                        rect.x1 + 10,
                        rect.y0 - 2,
                        rect.x1 + 10 + estimated_width,
                        rect.y0 + 16
                    )
                    
                    # 边界检查
                    if var_rect.x1 > page.rect.width - SAFE_MARGIN:
                        # 调整到左侧
                        var_rect = fitz.Rect(
                            rect.x0 - 10 - estimated_width,
                            rect.y0 - 2,
                            rect.x0 - 10,
                            rect.y0 + 16
                        )
                        print(f"调整{varnam}到左侧，避免超出页面")
                    
                    print(f"{varnam}位置: {var_rect}, 宽度: {var_rect.x1 - var_rect.x0:.1f}")
                
                # 添加变量注释
                try:
                    text_annot = page.add_freetext_annot(
                        var_rect,
                        varnam,
                        fontsize=9,
                        text_color=(0, 0, 0),
                        fill_color=domain_color
                    )
                    text_annot.set_border(width=1, style="solid")
                    text_annot.update()
                    
                    print(f"已添加变量注释: {varnam} for '{pdf_field_name}' at {var_rect}")
                    
                except Exception as e:
                    print(f"变量注释失败 {varnam}: {e}")
        
        # 保存最终无溢出版本
        doc.save(output_path)
        doc.close()
        
        print(f"\n最终无溢出版完成!")
        print(f"输出文件: {output_path}")
        
        return True
        
    except Exception as e:
        print(f"注释失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("最终无溢出版PDF注释脚本")
    print("="*60)
    
    print("溢出控制:")
    print("1. 设置安全边距20像素")
    print("2. 严格计算可用宽度")
    print("3. 动态调整颜色框大小和位置")
    print("4. 确保所有注释完全在页面内")
    
    # 检查PDF文件
    pdf_path = "HY1005-2023-2-P1_Unique eCRF_V3.0.pdf"
    if not os.path.exists(pdf_path):
        print(f"错误: PDF文件不存在 - {pdf_path}")
        exit(1)
    
    print("\n执行最终无溢出注释...")
    success = annotate_pdf_no_overflow()
    
    if success:
        print("\n最终无溢出版完成!")
        print("溢出控制结果:")
        print("1. 所有颜色框都有20像素安全边距")
        print("2. AGEU位置优化，确保不出界")
        print("3. 保持在'周岁'上方的合理位置")
        print("4. 字体在颜色框内完美显示")
        print("\n最终无溢出文件: HY1005-2023-2-P1_Unique eCRF_V3.0_NO_OVERFLOW_annotated.pdf")
    else:
        print("\n注释失败，请检查错误信息")
