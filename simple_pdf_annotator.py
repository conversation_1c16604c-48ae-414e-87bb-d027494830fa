import pandas as pd
import os
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import A4
from reportlab.lib.colors import Color, blue, yellow, green, orange, red
from reportlab.lib.units import mm

class SimplePDFAnnotator:
    """简单PDF注释器 - 使用reportlab创建注释PDF"""
    
    def __init__(self, excel_path):
        self.excel_path = excel_path
        self.excel_data = None
        self.annotation_data = None
        
        # 定义Domain颜色 (RGB 0-1范围)
        self.domain_colors = {
            'DM': {'color': Color(191/255, 1.0, 1.0), 'name': 'BLUE', 'rgb': '191, 255, 255'},
            'DS': {'color': Color(1.0, 1.0, 150/255), 'name': 'YELLOW', 'rgb': '255, 255, 150'},
            'SC': {'color': Color(150/255, 1.0, 150/255), 'name': 'GREEN', 'rgb': '150, 255, 150'},
            'VS': {'color': Color(1.0, 190/255, 155/255), 'name': 'ORANGE', 'rgb': '255, 190, 155'},
            'AE': {'color': Color(1.0, 150/255, 150/255), 'name': 'RED', 'rgb': '255, 150, 150'},
            'CM': {'color': Color(200/255, 150/255, 1.0), 'name': 'PURPLE', 'rgb': '200, 150, 255'}
        }
    
    def read_excel_data(self):
        """读取Excel数据"""
        try:
            self.excel_data = pd.read_excel(self.excel_path)
            print(f"成功读取Excel文件: {self.excel_path}")
            print(f"数据行数: {len(self.excel_data)}")
            return True
        except Exception as e:
            print(f"读取Excel文件失败: {e}")
            return False
    
    def prepare_annotation_data(self):
        """准备注释数据"""
        if self.excel_data is None:
            return False
        
        domain = self.excel_data['DOMAIN'].iloc[0] if 'DOMAIN' in self.excel_data.columns else 'Unknown'
        obsclass = self.excel_data['OBSCLASS'].iloc[0] if 'OBSCLASS' in self.excel_data.columns else 'Unknown'
        
        # 获取颜色信息
        color_info = self.domain_colors.get(domain, {
            'color': Color(191/255, 1.0, 1.0), 
            'name': 'BLUE', 
            'rgb': '191, 255, 255'
        })
        
        self.annotation_data = {
            'domain': domain,
            'obsclass': obsclass,
            'domain_label': f"{domain} ({obsclass})",
            'color_info': color_info,
            'variables': []
        }
        
        for _, row in self.excel_data.iterrows():
            var_info = {
                'varnam': str(row.get('VARNAM', 'N/A')),
                'varlabel': str(row.get('VARLABEL', 'N/A')),
                'order': row.get('ORDER', 0)
            }
            self.annotation_data['variables'].append(var_info)
        
        return True
    
    def create_annotated_pdf(self, output_path):
        """创建注释PDF"""
        try:
            # 创建PDF画布
            c = canvas.Canvas(output_path, pagesize=A4)
            width, height = A4
            
            # 页面标题
            c.setFont("Helvetica-Bold", 18)
            c.drawString(50, height - 50, "eCRF注释指南")
            
            # 基本信息
            c.setFont("Helvetica", 12)
            c.drawString(50, height - 80, f"基于文件: {self.excel_path}")
            c.drawString(50, height - 100, f"生成时间: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}")
            
            # Domain信息区域
            domain_y = height - 150
            c.setFont("Helvetica-Bold", 14)
            c.drawString(50, domain_y + 40, "1. Domain标识框样式:")
            
            # 绘制Domain标识框
            color_info = self.annotation_data['color_info']
            
            # Domain框
            domain_box_width = 250
            domain_box_height = 40
            
            c.setFillColor(color_info['color'])
            c.setStrokeColor('black')
            c.setLineWidth(2)
            c.rect(50, domain_y, domain_box_width, domain_box_height, fill=1, stroke=1)
            
            # Domain文本
            c.setFillColor('black')
            c.setFont("Helvetica-Bold", 14)
            text_x = 50 + 10
            text_y = domain_y + 15
            c.drawString(text_x, text_y, self.annotation_data['domain_label'])
            
            # RGB值标注
            c.setFont("Helvetica", 10)
            c.drawString(50 + domain_box_width + 20, domain_y + 20, color_info['rgb'])
            
            # 颜色说明
            c.setFont("Helvetica", 10)
            c.drawString(50, domain_y - 20, f"颜色: {color_info['name']} (RGB: {color_info['rgb']})")
            c.drawString(50, domain_y - 35, "位置: 表单顶部或相关区域")
            
            # 变量注释区域
            var_section_y = domain_y - 80
            c.setFont("Helvetica-Bold", 14)
            c.drawString(50, var_section_y, "2. 变量注释框样式:")
            
            # 绘制变量注释框
            var_y = var_section_y - 40
            vars_per_row = 2
            var_box_width = 150
            var_box_height = 30
            var_spacing_x = 180
            var_spacing_y = 60
            
            for i, var in enumerate(self.annotation_data['variables']):
                # 计算位置
                row = i // vars_per_row
                col = i % vars_per_row
                
                box_x = 50 + col * var_spacing_x
                box_y = var_y - row * var_spacing_y
                
                # 检查是否需要新页面
                if box_y < 150:
                    c.showPage()
                    c.setFont("Helvetica-Bold", 14)
                    var_y = height - 50
                    box_y = var_y - row * var_spacing_y
                
                # 绘制变量框
                c.setFillColor(color_info['color'])
                c.setStrokeColor('black')
                c.setLineWidth(1)
                c.rect(box_x, box_y, var_box_width, var_box_height, fill=1, stroke=1)
                
                # 变量名
                c.setFillColor('black')
                c.setFont("Helvetica-Bold", 11)
                c.drawString(box_x + 5, box_y + 15, var['varnam'])
                
                # 变量标签（在框下方）
                c.setFont("Helvetica", 9)
                label_text = var['varlabel']
                if len(label_text) > 20:
                    label_text = label_text[:17] + "..."
                c.drawString(box_x, box_y - 15, f"标签: {label_text}")
                
                # 完整标签（在框下方第二行）
                if len(var['varlabel']) > 20:
                    c.setFont("Helvetica", 8)
                    c.drawString(box_x, box_y - 28, f"完整: {var['varlabel']}")
            
            # 新页面 - 详细说明
            c.showPage()
            c.setFont("Helvetica-Bold", 16)
            c.drawString(50, height - 50, "注释实施指南")
            
            # 实施步骤
            steps_y = height - 100
            c.setFont("Helvetica-Bold", 12)
            c.drawString(50, steps_y, "实施步骤:")
            
            steps = [
                "1. 在eCRF表单顶部添加Domain标识框",
                "2. 使用指定的背景颜色和边框样式",
                "3. 在每个数据字段旁边添加对应的变量注释框",
                "4. 确保注释框不遮挡原有表单内容",
                "5. 保持同一Domain内所有注释的颜色一致性"
            ]
            
            step_y = steps_y - 30
            c.setFont("Helvetica", 11)
            for step in steps:
                c.drawString(70, step_y, step)
                step_y -= 25
            
            # 变量详细列表
            detail_y = step_y - 40
            c.setFont("Helvetica-Bold", 12)
            c.drawString(50, detail_y, "变量详细信息:")
            
            detail_y -= 30
            c.setFont("Helvetica", 10)
            for i, var in enumerate(self.annotation_data['variables'], 1):
                if detail_y < 100:
                    c.showPage()
                    detail_y = height - 50
                
                c.drawString(70, detail_y, f"{i}. {var['varnam']} → {var['varlabel']}")
                detail_y -= 20
            
            # 技术规格
            tech_y = detail_y - 40
            c.setFont("Helvetica-Bold", 12)
            c.drawString(50, tech_y, "技术规格:")
            
            tech_specs = [
                f"Domain: {self.annotation_data['domain']}",
                f"OBSCLASS: {self.annotation_data['obsclass']}",
                f"颜色标准: {color_info['name']} (RGB: {color_info['rgb']})",
                f"Domain框: 2px黑色边框",
                f"变量框: 1px黑色边框",
                f"字体: Helvetica, 黑色"
            ]
            
            tech_y -= 30
            c.setFont("Helvetica", 10)
            for spec in tech_specs:
                c.drawString(70, tech_y, spec)
                tech_y -= 20
            
            # 保存PDF
            c.save()
            print(f"注释PDF已创建: {output_path}")
            return True
            
        except Exception as e:
            print(f"创建PDF失败: {e}")
            import traceback
            traceback.print_exc()
            return False

def main():
    """主函数"""
    excel_path = "dm_spec.xlsx"
    
    print("简单PDF注释器")
    print("="*50)
    
    # 检查Excel文件
    if not os.path.exists(excel_path):
        print(f"错误: Excel文件不存在 - {excel_path}")
        return
    
    # 创建注释器
    annotator = SimplePDFAnnotator(excel_path)
    
    # 读取Excel数据
    if not annotator.read_excel_data():
        return
    
    # 准备注释数据
    if not annotator.prepare_annotation_data():
        print("准备注释数据失败")
        return
    
    # 显示注释信息
    print(f"\n注释信息:")
    print(f"Domain: {annotator.annotation_data['domain']}")
    print(f"OBSCLASS: {annotator.annotation_data['obsclass']}")
    print(f"Domain标签: {annotator.annotation_data['domain_label']}")
    print(f"颜色: {annotator.annotation_data['color_info']['name']}")
    print(f"变量数量: {len(annotator.annotation_data['variables'])}")
    
    # 创建注释PDF
    output_pdf = "ecrf_annotation_guide.pdf"
    
    if annotator.create_annotated_pdf(output_pdf):
        print(f"\n✅ 成功创建注释PDF: {output_pdf}")
        print("\n📋 PDF内容包括:")
        print("- Domain标识框样式示例")
        print("- 变量注释框样式示例") 
        print("- 详细的实施指南")
        print("- 技术规格说明")
        print("\n💡 使用建议:")
        print("1. 打开生成的PDF查看注释样式")
        print("2. 使用PDF编辑器在原始eCRF上添加相应注释")
        print("3. 参考PDF中的颜色和尺寸规格")
    else:
        print("❌ 创建注释PDF失败")

if __name__ == "__main__":
    main()
