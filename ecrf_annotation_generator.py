import pandas as pd
import json
import os
from datetime import datetime

class eCRFAnnotationGenerator:
    """eCRF注释生成器 - 生成详细的注释指南"""
    
    def __init__(self, excel_path):
        self.excel_path = excel_path
        self.excel_data = None
        self.annotation_data = None
        
        # Domain颜色标准
        self.domain_colors = {
            'DM': {'name': 'BLUE', 'rgb': '191, 255, 255', 'hex': '#BFFFFF', 'desc': 'Demographics'},
            'DS': {'name': 'YELLOW', 'rgb': '255, 255, 150', 'hex': '#FFFF96', 'desc': 'Disposition'},
            'SC': {'name': 'GRE<PERSON>', 'rgb': '150, 255, 150', 'hex': '#96FF96', 'desc': 'Subject Characteristics'},
            'VS': {'name': 'ORANGE', 'rgb': '255, 190, 155', 'hex': '#FFBE9B', 'desc': 'Vital Signs'},
            'AE': {'name': 'RED', 'rgb': '255, 150, 150', 'hex': '#FF9696', 'desc': 'Adverse Events'},
            'CM': {'name': 'PURPLE', 'rgb': '200, 150, 255', 'hex': '#C896FF', 'desc': 'Concomitant Medications'}
        }
    
    def read_excel_data(self):
        """读取Excel数据"""
        try:
            self.excel_data = pd.read_excel(self.excel_path)
            print(f"成功读取Excel文件: {self.excel_path}")
            print(f"数据行数: {len(self.excel_data)}")
            return True
        except Exception as e:
            print(f"读取Excel文件失败: {e}")
            return False
    
    def prepare_annotation_data(self):
        """准备注释数据"""
        if self.excel_data is None:
            return False
        
        domain = self.excel_data['DOMAIN'].iloc[0] if 'DOMAIN' in self.excel_data.columns else 'Unknown'
        obsclass = self.excel_data['OBSCLASS'].iloc[0] if 'OBSCLASS' in self.excel_data.columns else 'Unknown'
        
        color_info = self.domain_colors.get(domain, {
            'name': 'BLUE', 'rgb': '191, 255, 255', 'hex': '#BFFFFF', 'desc': 'Unknown'
        })
        
        self.annotation_data = {
            'domain': domain,
            'obsclass': obsclass,
            'domain_label': f"{domain} ({obsclass})",
            'color_info': color_info,
            'variables': []
        }
        
        for _, row in self.excel_data.iterrows():
            var_info = {
                'varnam': str(row.get('VARNAM', 'N/A')),
                'varlabel': str(row.get('VARLABEL', 'N/A')),
                'order': row.get('ORDER', 0),
                'crf_page': row.get('CRF_P_NO', 'Unknown'),
                'deftype': row.get('DEFTYPE', 'Unknown'),
                'origin': row.get('ORIGIN', 'Unknown')
            }
            self.annotation_data['variables'].append(var_info)
        
        return True
    
    def generate_html_annotation_guide(self, output_file):
        """生成HTML注释指南"""
        color_info = self.annotation_data['color_info']
        
        html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>eCRF注释实施指南</title>
    <style>
        body {{ 
            font-family: 'Microsoft YaHei', Arial, sans-serif; 
            margin: 20px; 
            line-height: 1.6; 
            background-color: #f5f5f5;
        }}
        .container {{ 
            max-width: 1200px; 
            margin: 0 auto; 
            background-color: white; 
            padding: 30px; 
            border-radius: 10px; 
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }}
        .header {{ 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white; 
            padding: 30px; 
            border-radius: 10px; 
            margin-bottom: 30px; 
            text-align: center;
        }}
        .section {{ 
            margin: 30px 0; 
            padding: 20px; 
            border: 1px solid #ddd; 
            border-radius: 8px; 
            background-color: #fafafa;
        }}
        .domain-box {{ 
            background-color: {color_info['hex']}; 
            border: 3px solid #000; 
            padding: 15px 20px; 
            margin: 15px 0; 
            display: inline-block;
            font-weight: bold;
            font-size: 18px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }}
        .variable-box {{ 
            background-color: {color_info['hex']}; 
            border: 2px solid #000; 
            padding: 10px 15px; 
            margin: 8px; 
            display: inline-block;
            font-size: 14px;
            border-radius: 4px;
            font-weight: bold;
            box-shadow: 0 1px 3px rgba(0,0,0,0.2);
        }}
        .color-info {{ 
            background-color: #e8f4fd; 
            padding: 15px; 
            border-radius: 5px; 
            margin: 15px 0;
            border-left: 5px solid #2196F3;
        }}
        .step-list {{ 
            background-color: #f0f8ff; 
            padding: 20px; 
            border-radius: 5px; 
            margin: 15px 0;
        }}
        .step-list ol {{ 
            margin: 0; 
            padding-left: 20px;
        }}
        .step-list li {{ 
            margin: 10px 0; 
            font-size: 16px;
        }}
        table {{ 
            border-collapse: collapse; 
            width: 100%; 
            margin: 20px 0; 
            background-color: white;
        }}
        th, td {{ 
            border: 1px solid #ddd; 
            padding: 12px; 
            text-align: left; 
        }}
        th {{ 
            background-color: #f2f2f2; 
            font-weight: bold; 
            color: #333;
        }}
        .highlight {{ 
            background-color: #fff3cd; 
            padding: 15px; 
            border-radius: 5px; 
            border-left: 5px solid #ffc107; 
            margin: 15px 0;
        }}
        .print-section {{ 
            page-break-before: always; 
        }}
        @media print {{
            body {{ background-color: white; }}
            .container {{ box-shadow: none; }}
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📋 eCRF注释实施指南</h1>
            <p>基于SDTM规范的电子病例报告表注释标准</p>
            <p>生成时间: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}</p>
        </div>

        <div class="section">
            <h2>📊 基本信息</h2>
            <div class="color-info">
                <p><strong>源文件:</strong> {self.excel_path}</p>
                <p><strong>Domain:</strong> {self.annotation_data['domain']}</p>
                <p><strong>OBSCLASS:</strong> {self.annotation_data['obsclass']}</p>
                <p><strong>变量数量:</strong> {len(self.annotation_data['variables'])}</p>
                <p><strong>标准颜色:</strong> {color_info['name']} (RGB: {color_info['rgb']})</p>
            </div>
        </div>

        <div class="section">
            <h2>🎨 Domain标识框样式</h2>
            <p>在eCRF表单顶部或相关区域添加以下样式的Domain标识框：</p>
            
            <div style="text-align: center; margin: 20px 0;">
                <div class="domain-box">{self.annotation_data['domain_label']}</div>
            </div>
            
            <div class="color-info">
                <h4>技术规格:</h4>
                <ul>
                    <li><strong>背景颜色:</strong> {color_info['name']} (RGB: {color_info['rgb']}, HEX: {color_info['hex']})</li>
                    <li><strong>边框:</strong> 3px 黑色实线</li>
                    <li><strong>字体:</strong> 粗体, 18px</li>
                    <li><strong>内边距:</strong> 15px 20px</li>
                    <li><strong>圆角:</strong> 5px</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2>🏷️ 变量注释框样式</h2>
            <p>在每个数据字段旁边添加对应的变量注释框：</p>
            
            <div style="margin: 20px 0;">
"""
        
        # 添加变量框
        for var in self.annotation_data['variables']:
            html_content += f'                <div class="variable-box">{var["varnam"]}</div>\n'
        
        html_content += f"""
            </div>
            
            <div class="color-info">
                <h4>技术规格:</h4>
                <ul>
                    <li><strong>背景颜色:</strong> 与Domain框相同 ({color_info['hex']})</li>
                    <li><strong>边框:</strong> 2px 黑色实线</li>
                    <li><strong>字体:</strong> 粗体, 14px</li>
                    <li><strong>内边距:</strong> 10px 15px</li>
                    <li><strong>圆角:</strong> 4px</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2>📋 实施步骤</h2>
            <div class="step-list">
                <ol>
                    <li><strong>打开PDF编辑器</strong> (如Adobe Acrobat, Foxit PDF Editor等)</li>
                    <li><strong>加载原始eCRF文件</strong>: HY1005-2023-2-P1_Unique eCRF_V3.0.pdf</li>
                    <li><strong>添加Domain标识框</strong>: 在表单顶部添加"{self.annotation_data['domain_label']}"标识</li>
                    <li><strong>设置Domain框样式</strong>: 使用{color_info['name']}背景色 (RGB: {color_info['rgb']})</li>
                    <li><strong>添加变量注释</strong>: 在每个相关字段旁边添加对应的VARNAM注释</li>
                    <li><strong>保持一致性</strong>: 确保所有注释使用相同的颜色和样式</li>
                    <li><strong>检查布局</strong>: 确保注释不遮挡原有内容</li>
                    <li><strong>保存文件</strong>: 另存为带注释的eCRF版本</li>
                </ol>
            </div>
        </div>

        <div class="section">
            <h2>📝 变量详细信息</h2>
            <table>
                <tr>
                    <th>序号</th>
                    <th>变量名 (VARNAM)</th>
                    <th>变量标签 (VARLABEL)</th>
                    <th>顺序</th>
                    <th>CRF页码</th>
                    <th>定义类型</th>
                    <th>来源</th>
                </tr>
"""
        
        # 添加变量详细信息
        for i, var in enumerate(self.annotation_data['variables'], 1):
            html_content += f"""
                <tr>
                    <td>{i}</td>
                    <td><strong>{var['varnam']}</strong></td>
                    <td>{var['varlabel']}</td>
                    <td>{var['order']}</td>
                    <td>{var['crf_page']}</td>
                    <td>{var['deftype']}</td>
                    <td>{var['origin']}</td>
                </tr>
"""
        
        html_content += f"""
            </table>
        </div>

        <div class="section">
            <h2>⚠️ 重要提示</h2>
            <div class="highlight">
                <h4>注意事项:</h4>
                <ul>
                    <li>确保注释颜色与CDISC标准一致</li>
                    <li>注释框不应遮挡原有的表单内容</li>
                    <li>保持同一Domain内所有注释的视觉一致性</li>
                    <li>变量注释应放置在相应数据字段的明显位置</li>
                    <li>建议在注释完成后进行全面检查</li>
                </ul>
            </div>
        </div>

        <div class="section print-section">
            <h2>🎨 颜色标准参考</h2>
            <table>
                <tr><th>Domain</th><th>颜色名称</th><th>RGB值</th><th>十六进制</th><th>说明</th></tr>
"""
        
        # 添加所有Domain颜色标准
        for domain, info in self.domain_colors.items():
            html_content += f"""
                <tr>
                    <td><strong>{domain}</strong></td>
                    <td>{info['name']}</td>
                    <td>{info['rgb']}</td>
                    <td>{info['hex']}</td>
                    <td>{info['desc']}</td>
                </tr>
"""
        
        html_content += """
            </table>
        </div>

        <div class="section">
            <h2>📞 技术支持</h2>
            <p>如果在实施过程中遇到问题，请参考以下资源：</p>
            <ul>
                <li>CDISC SDTM Implementation Guide</li>
                <li>FDA Study Data Technical Conformance Guide</li>
                <li>本指南生成的配套文件 (JSON, CSV格式)</li>
            </ul>
        </div>
    </div>
</body>
</html>
"""
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(html_content)
        print(f"HTML注释指南已生成: {output_file}")
    
    def generate_detailed_csv(self, output_file):
        """生成详细的CSV实施指南"""
        rows = []
        
        # Domain行
        color_info = self.annotation_data['color_info']
        rows.append({
            'Type': 'Domain',
            'Element': 'Domain标识框',
            'Position': '表单顶部',
            'Text': self.annotation_data['domain_label'],
            'Background_Color': color_info['name'],
            'RGB': color_info['rgb'],
            'Hex': color_info['hex'],
            'Border': '3px 黑色实线',
            'Font': '粗体 18px',
            'Padding': '15px 20px',
            'Description': f"{self.annotation_data['domain']} Domain标识",
            'Implementation_Notes': '放置在表单最顶部或相关区域的显著位置'
        })
        
        # 变量行
        for i, var in enumerate(self.annotation_data['variables'], 1):
            rows.append({
                'Type': 'Variable',
                'Element': f'变量注释框 {i}',
                'Position': f'{var["varlabel"]}字段旁边',
                'Text': var['varnam'],
                'Background_Color': color_info['name'],
                'RGB': color_info['rgb'],
                'Hex': color_info['hex'],
                'Border': '2px 黑色实线',
                'Font': '粗体 14px',
                'Padding': '10px 15px',
                'Description': var['varlabel'],
                'Implementation_Notes': f'放置在{var["varlabel"]}数据输入字段的旁边或上方'
            })
        
        df = pd.DataFrame(rows)
        df.to_csv(output_file, index=False, encoding='utf-8-sig')
        print(f"详细CSV指南已生成: {output_file}")
    
    def generate_json_data(self, output_file):
        """生成JSON格式的注释数据"""
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(self.annotation_data, f, ensure_ascii=False, indent=2)
        print(f"JSON数据文件已生成: {output_file}")
    
    def generate_text_report(self, output_file):
        """生成文本格式的实施报告"""
        color_info = self.annotation_data['color_info']
        
        report_lines = [
            "="*80,
            "eCRF注释实施报告",
            "="*80,
            f"生成时间: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}",
            f"源文件: {self.excel_path}",
            "",
            "基本信息:",
            f"- Domain: {self.annotation_data['domain']}",
            f"- OBSCLASS: {self.annotation_data['obsclass']}",
            f"- Domain标签: {self.annotation_data['domain_label']}",
            f"- 标准颜色: {color_info['name']} (RGB: {color_info['rgb']})",
            f"- 变量数量: {len(self.annotation_data['variables'])}",
            "",
            "Domain标识框规格:",
            f"- 文本: {self.annotation_data['domain_label']}",
            f"- 背景色: {color_info['hex']}",
            "- 边框: 3px 黑色实线",
            "- 字体: 粗体 18px",
            "- 位置: 表单顶部",
            "",
            "变量注释框规格:",
            f"- 背景色: {color_info['hex']}",
            "- 边框: 2px 黑色实线", 
            "- 字体: 粗体 14px",
            "- 位置: 相应字段旁边",
            "",
            "变量列表:",
        ]
        
        for i, var in enumerate(self.annotation_data['variables'], 1):
            report_lines.append(f"  {i:2d}. {var['varnam']:12s} → {var['varlabel']}")
        
        report_lines.extend([
            "",
            "实施步骤:",
            "1. 使用PDF编辑器打开原始eCRF文件",
            "2. 在表单顶部添加Domain标识框",
            "3. 在每个相关字段旁边添加变量注释框",
            "4. 确保使用正确的颜色和样式",
            "5. 检查注释不遮挡原有内容",
            "6. 保存带注释的eCRF文件",
            "",
            "="*80
        ])
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(report_lines))
        print(f"文本报告已生成: {output_file}")

def main():
    """主函数"""
    excel_path = "dm_spec.xlsx"
    
    print("eCRF注释生成器")
    print("="*60)
    
    if not os.path.exists(excel_path):
        print(f"Excel文件不存在: {excel_path}")
        return
    
    # 创建生成器
    generator = eCRFAnnotationGenerator(excel_path)
    
    # 读取数据
    if not generator.read_excel_data():
        return
    
    # 准备注释数据
    if not generator.prepare_annotation_data():
        print("准备注释数据失败")
        return
    
    # 显示基本信息
    print(f"\n注释信息:")
    print(f"Domain: {generator.annotation_data['domain']}")
    print(f"OBSCLASS: {generator.annotation_data['obsclass']}")
    print(f"Domain标签: {generator.annotation_data['domain_label']}")
    print(f"颜色: {generator.annotation_data['color_info']['name']}")
    print(f"变量数量: {len(generator.annotation_data['variables'])}")

    # 生成所有输出文件
    base_name = "ecrf_annotation_complete"

    print(f"\n正在生成输出文件...")
    
    # HTML指南 (主要文件)
    html_file = f"{base_name}.html"
    generator.generate_html_annotation_guide(html_file)
    
    # CSV实施指南
    csv_file = f"{base_name}.csv"
    generator.generate_detailed_csv(csv_file)
    
    # JSON数据
    json_file = f"{base_name}.json"
    generator.generate_json_data(json_file)
    
    # 文本报告
    txt_file = f"{base_name}.txt"
    generator.generate_text_report(txt_file)
    
    print(f"\n所有文件生成完成!")
    print(f"生成的文件:")
    print(f"  {html_file} - 完整的HTML实施指南 (主要文件)")
    print(f"  {csv_file} - 详细的CSV实施表")
    print(f"  {json_file} - JSON格式数据")
    print(f"  {txt_file} - 文本格式报告")

    print(f"\n下一步操作:")
    print(f"1. 打开 {html_file} 查看完整的实施指南")
    print(f"2. 使用PDF编辑器打开原始eCRF文件")
    print(f"3. 按照指南添加相应的注释框")
    print(f"4. 参考CSV文件了解具体的技术规格")

if __name__ == "__main__":
    main()
