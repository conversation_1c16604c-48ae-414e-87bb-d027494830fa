#!/usr/bin/env python3
"""
修正后的PDF注释脚本
根据用户反馈和PDF实际内容进行精确匹配
"""

import fitz  # PyMuPDF
import os

def annotate_pdf():
    """在PDF上添加注释 - 使用PDF中的实际字段名"""
    
    # 文件路径
    pdf_path = "HY1005-2023-2-P1_Unique eCRF_V3.0.pdf"
    output_path = "HY1005-2023-2-P1_Unique eCRF_V3.0_corrected_annotated.pdf"
    
    # 颜色定义 (fitz 0-1 浮点格式) - DM域颜色
    domain_color = (0.75, 1, 1)  # RGB(191,255,255)
    
    # 建立正确的映射关系：PDF实际字段名 -> VARNAM
    field_mappings = {
        "出生日期": "BRTHDTC",    # 不是"出生日期/时间"
        "年龄": "AGE",
        "性别": "SEX",
        # 注意：年龄单位(AGEU)在PDF中可能不存在作为独立字段
    }
    
    try:
        # 打开PDF文件
        doc = fitz.open(pdf_path)
        print(f"成功打开PDF: {pdf_path}")
        print(f"总页数: {len(doc)}")
        
        # 搜索关键词 - 查找Domain标识位置
        search_terms = ['人口学资料', 'Demographics', 'DEMOGRAPHICS', '人口学', 'Demography']
        domain_added = False
        
        # 遍历所有页面
        for page_num in range(len(doc)):
            page = doc[page_num]
            print(f"\n处理第 {page_num + 1} 页...")
            
            # 1. 查找并添加Domain标识
            if not domain_added:
                for term in search_terms:
                    text_instances = page.search_for(term)
                    if text_instances:
                        rect = text_instances[0]
                        print(f"找到Domain标识 '{term}' at {rect}")
                        
                        # 在左上方添加Domain框 - 调整位置使其更明显
                        domain_rect = fitz.Rect(
                            rect.x0 - 5,      # 稍微左移
                            rect.y0 - 35,     # 上方留更多空间
                            rect.x0 + 120,    # 宽度
                            rect.y0 - 10      # 高度
                        )
                        
                        # 添加彩色矩形 - 增加透明度
                        annot = page.add_rect_annot(domain_rect)
                        annot.set_colors(stroke=(0, 0, 0), fill=domain_color)  # 黑色边框
                        annot.set_border(width=2)
                        annot.set_opacity(0.8)  # 设置透明度
                        annot.update()
                        
                        # 添加文字 - 使用更大字体
                        text_point = fitz.Point(domain_rect.x0 + 8, domain_rect.y0 + 18)
                        page.insert_text(text_point, "DM(人口学资料)", 
                                       fontsize=12, color=(0, 0, 0), fontname="helv")
                        
                        print(f"已添加Domain注释: DM(人口学资料) at {domain_rect}")
                        domain_added = True
                        break
            
            # 2. 查找并添加变量注释 - 使用PDF中的实际字段名
            for pdf_field_name, varnam in field_mappings.items():
                text_instances = page.search_for(pdf_field_name)
                if text_instances:
                    rect = text_instances[0]
                    print(f"找到字段 '{pdf_field_name}' at {rect}")
                    
                    # 在右侧添加变量框 - 调整位置和大小
                    var_rect = fitz.Rect(
                        rect.x1 + 15,     # 右侧留更多空间
                        rect.y0 - 2,      # 稍微上移对齐
                        rect.x1 + 100,    # 增加宽度
                        rect.y0 + 18      # 增加高度
                    )
                    
                    # 添加彩色矩形
                    annot = page.add_rect_annot(var_rect)
                    annot.set_colors(stroke=(0, 0, 0), fill=domain_color)
                    annot.set_border(width=1)
                    annot.set_opacity(0.8)
                    annot.update()
                    
                    # 添加文字 - 使用更大字体
                    text_point = fitz.Point(var_rect.x0 + 5, var_rect.y0 + 12)
                    page.insert_text(text_point, varnam, fontsize=10, color=(0, 0, 0), fontname="helv")
                    
                    print(f"已添加变量注释: {varnam} for '{pdf_field_name}' at {var_rect}")
                else:
                    print(f"未找到字段: {pdf_field_name}")
        
        # 保存注释后的PDF
        doc.save(output_path)
        doc.close()
        
        print(f"\n注释完成!")
        print(f"输出文件: {output_path}")
        
        return True
        
    except Exception as e:
        print(f"注释失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def analyze_pdf_content():
    """分析PDF内容，查找所有可能的字段"""
    pdf_path = "HY1005-2023-2-P1_Unique eCRF_V3.0.pdf"
    
    try:
        doc = fitz.open(pdf_path)
        print("分析PDF内容，查找人口学资料相关字段...")
        
        # 根据用户截图，这些是可能的字段名
        target_fields = ["出生日期", "年龄", "性别", "民族", "其他民族", "身高", "体重", "BMI"]
        
        for page_num in range(min(10, len(doc))):  # 只检查前10页
            page = doc[page_num]
            print(f"\n第 {page_num + 1} 页:")
            
            for field in target_fields:
                text_instances = page.search_for(field)
                if text_instances:
                    print(f"  找到字段 '{field}' at {text_instances[0]}")
        
        doc.close()
        
    except Exception as e:
        print(f"分析失败: {e}")

if __name__ == "__main__":
    print("修正后的PDF注释脚本")
    print("="*50)
    
    # 检查依赖
    try:
        import fitz
        print("PyMuPDF (fitz) 已安装")
    except ImportError:
        print("请先安装PyMuPDF: pip install pymupdf")
        exit(1)
    
    # 检查PDF文件
    pdf_path = "HY1005-2023-2-P1_Unique eCRF_V3.0.pdf"
    if not os.path.exists(pdf_path):
        print(f"错误: PDF文件不存在 - {pdf_path}")
        exit(1)
    
    # 先分析PDF内容
    print("\n1. 分析PDF内容...")
    analyze_pdf_content()
    
    # 执行注释
    print("\n2. 执行注释...")
    success = annotate_pdf()
    
    if success:
        print("\n修正完成!")
        print("主要修正:")
        print("1. 使用PDF中的实际字段名 '出生日期' 而不是 '出生日期/时间'")
        print("2. 移除了PDF中不存在的 '年龄单位' 字段")
        print("3. 调整了注释的位置、大小和透明度")
        print("4. 增加了字体大小和边框，使注释更明显")
    else:
        print("\n注释失败，请检查错误信息")
