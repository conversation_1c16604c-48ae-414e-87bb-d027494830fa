#!/usr/bin/env python3
"""
安全位置版PDF注释脚本
详细分析周岁周围环境，找到真正安全的AGEU位置
"""

import fitz  # PyMuPDF
import os

def analyze_zhousu_environment():
    """详细分析"周岁"周围环境"""
    pdf_path = "HY1005-2023-2-P1_Unique eCRF_V3.0.pdf"
    
    try:
        doc = fitz.open(pdf_path)
        page = doc[6]  # 第7页
        
        # 找到"周岁"
        text_instances = page.search_for("周岁")
        if text_instances:
            rect = text_instances[0]
            print(f"'周岁'精确位置: {rect}")
            print(f"页面尺寸: {page.rect}")
            
            # 详细分析周围更大范围的区域
            areas = {
                "远右侧": fitz.Rect(rect.x1 + 10, rect.y0 - 10, rect.x1 + 100, rect.y1 + 10),
                "远左侧": fitz.Rect(rect.x0 - 100, rect.y0 - 10, rect.x0 - 10, rect.y1 + 10),
                "远上方": fitz.Rect(rect.x0 - 20, rect.y0 - 40, rect.x1 + 20, rect.y0 - 5),
                "远下方": fitz.Rect(rect.x0 - 20, rect.y1 + 5, rect.x1 + 20, rect.y1 + 40),
                "右上角": fitz.Rect(rect.x1 + 5, rect.y0 - 20, rect.x1 + 60, rect.y0 + 5),
                "左上角": fitz.Rect(rect.x0 - 60, rect.y0 - 20, rect.x0 - 5, rect.y0 + 5),
                "右下角": fitz.Rect(rect.x1 + 5, rect.y1 - 5, rect.x1 + 60, rect.y1 + 20),
                "左下角": fitz.Rect(rect.x0 - 60, rect.y1 - 5, rect.x0 - 5, rect.y1 + 20)
            }
            
            print(f"\n详细环境分析:")
            for direction, area in areas.items():
                # 检查是否超出页面边界
                if (area.x0 < 0 or area.y0 < 0 or 
                    area.x1 > page.rect.width or area.y1 > page.rect.height):
                    print(f"{direction:8s} {area}: 超出页面边界")
                else:
                    text_content = page.get_textbox(area)
                    print(f"{direction:8s} {area}: '{text_content.strip()}'")
        
        doc.close()
        
    except Exception as e:
        print(f"分析失败: {e}")

def annotate_pdf_safe_position():
    """安全位置版注释"""
    
    pdf_path = "HY1005-2023-2-P1_Unique eCRF_V3.0.pdf"
    output_path = "HY1005-2023-2-P1_Unique eCRF_V3.0_SAFE_POSITION_annotated.pdf"
    
    # DM域颜色
    domain_color = (0.75, 1, 1)  # RGB(191,255,255)
    
    # 严格按照Excel数据的字段映射
    field_mappings = {
        "出生日期": "BRTHDTC",
        "年龄": "AGE",
        "周岁": "AGEU",
        "性别": "SEX"
    }
    
    try:
        doc = fitz.open(pdf_path)
        print(f"开始安全位置注释...")
        
        # 只处理第7页
        target_page = 6  # 第7页，索引为6
        page = doc[target_page]
        print(f"\n只处理第 {target_page + 1} 页...")
        
        # 1. 添加Domain标识
        text_instances = page.search_for("人口学资料")
        for rect in text_instances:
            if rect.y0 < page.rect.height * 0.3:
                print(f"找到表单标题 '人口学资料' at {rect}")
                
                domain_rect = fitz.Rect(
                    rect.x0,
                    rect.y0 - 25,
                    rect.x0 + 120,
                    rect.y0 - 5
                )
                
                try:
                    text_annot = page.add_freetext_annot(
                        domain_rect,
                        "DM(人口学资料)",
                        fontsize=10,
                        text_color=(0, 0, 0),
                        fill_color=domain_color
                    )
                    text_annot.set_border(width=2, style="solid")
                    text_annot.update()
                    
                    print(f"已添加Domain标识: DM(人口学资料) at {domain_rect}")
                    break
                    
                except Exception as e:
                    print(f"Domain注释失败: {e}")
                    break
        
        # 2. 添加变量注释 - 为AGEU找到真正安全的位置
        for pdf_field_name, varnam in field_mappings.items():
            text_instances = page.search_for(pdf_field_name)
            if text_instances:
                rect = text_instances[0]
                print(f"找到字段 '{pdf_field_name}' at {rect}")
                
                if pdf_field_name == "周岁":
                    print(f"为AGEU寻找真正安全的位置...")
                    
                    # 基于之前的分析，尝试几个候选位置
                    candidate_positions = [
                        # 位置1: 在"周岁"右侧，但距离更远
                        fitz.Rect(rect.x1 + 15, rect.y0 - 1, rect.x1 + 60, rect.y0 + 14),
                        # 位置2: 在"周岁"左侧，但距离更远  
                        fitz.Rect(rect.x0 - 70, rect.y0 - 1, rect.x0 - 25, rect.y0 + 14),
                        # 位置3: 在"周岁"下方，但距离更远
                        fitz.Rect(rect.x0, rect.y1 + 8, rect.x0 + 45, rect.y1 + 23),
                        # 位置4: 在页面右边缘的空白处
                        fitz.Rect(page.rect.width - 80, rect.y0 - 1, page.rect.width - 20, rect.y0 + 14)
                    ]
                    
                    # 测试每个候选位置
                    best_position = None
                    for i, pos in enumerate(candidate_positions):
                        # 检查是否在页面内
                        if (pos.x0 >= 10 and pos.y0 >= 10 and 
                            pos.x1 <= page.rect.width - 10 and pos.y1 <= page.rect.height - 10):
                            
                            # 检查该位置是否有内容
                            check_area = fitz.Rect(pos.x0 - 5, pos.y0 - 5, pos.x1 + 5, pos.y1 + 5)
                            check_text = page.get_textbox(check_area)
                            
                            print(f"候选位置{i+1}: {pos}")
                            print(f"  页面内: 是")
                            print(f"  该区域内容: '{check_text.strip()}'")
                            
                            if not check_text.strip():
                                print(f"  选择位置{i+1}: 完全空白，安全!")
                                best_position = pos
                                break
                            else:
                                print(f"  位置{i+1}有内容，跳过")
                        else:
                            print(f"候选位置{i+1}: {pos} - 超出页面边界")
                    
                    if best_position:
                        var_rect = best_position
                        print(f"AGEU最终安全位置: {var_rect}")
                    else:
                        # 如果所有位置都有问题，使用最保守的位置
                        var_rect = fitz.Rect(
                            page.rect.width - 70,  # 页面右边缘
                            rect.y0 - 1,
                            page.rect.width - 15,
                            rect.y0 + 14
                        )
                        print(f"使用保守位置: {var_rect}")
                
                else:
                    # 其他字段的常规处理
                    var_rect = fitz.Rect(
                        rect.x1 + 10,
                        rect.y0 - 2,
                        rect.x1 + 70,
                        rect.y0 + 16
                    )
                
                # 最终边界检查
                if var_rect.x1 > page.rect.width - 10:
                    var_rect = fitz.Rect(
                        page.rect.width - 70,
                        var_rect.y0,
                        page.rect.width - 10,
                        var_rect.y1
                    )
                
                if var_rect.x0 < 10:
                    var_rect = fitz.Rect(10, var_rect.y0, 70, var_rect.y1)
                
                # 添加变量注释
                try:
                    text_annot = page.add_freetext_annot(
                        var_rect,
                        varnam,
                        fontsize=9,
                        text_color=(0, 0, 0),
                        fill_color=domain_color
                    )
                    text_annot.set_border(width=1, style="solid")
                    text_annot.update()
                    
                    print(f"已添加变量注释: {varnam} for '{pdf_field_name}' at {var_rect}")
                    
                except Exception as e:
                    print(f"变量注释失败 {varnam}: {e}")
        
        # 保存安全位置版本
        doc.save(output_path)
        doc.close()
        
        print(f"\n安全位置版完成!")
        print(f"输出文件: {output_path}")
        
        return True
        
    except Exception as e:
        print(f"注释失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("安全位置版PDF注释脚本")
    print("="*60)
    
    print("策略:")
    print("1. 详细分析'周岁'周围环境")
    print("2. 测试多个候选位置")
    print("3. 选择真正安全、不遮挡、不出界的位置")
    
    # 先详细分析环境
    print("\n详细分析'周岁'周围环境...")
    analyze_zhousu_environment()
    
    # 检查PDF文件
    pdf_path = "HY1005-2023-2-P1_Unique eCRF_V3.0.pdf"
    if not os.path.exists(pdf_path):
        print(f"错误: PDF文件不存在 - {pdf_path}")
        exit(1)
    
    print("\n执行安全位置注释...")
    success = annotate_pdf_safe_position()
    
    if success:
        print("\n安全位置版完成!")
        print("解决方案:")
        print("1. 测试了多个候选位置")
        print("2. 选择了真正安全的位置")
        print("3. 确保不遮挡、不出界")
        print("\n最终安全文件: HY1005-2023-2-P1_Unique eCRF_V3.0_SAFE_POSITION_annotated.pdf")
    else:
        print("\n注释失败，请检查错误信息")
