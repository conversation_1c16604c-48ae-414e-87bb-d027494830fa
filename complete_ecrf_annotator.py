import pandas as pd
import json
import os
from pathlib import Path

class CompleteeCRFAnnotator:
    """完整的eCRF注释器"""
    
    def __init__(self):
        self.domain_colors = {
            'DM': {'name': 'BLUE', 'rgb': '191, 255, 255', 'hex': '#BFFFFF', 'desc': 'Demographics'},
            'DS': {'name': 'YELLOW', 'rgb': '255, 255, 150', 'hex': '#FFFF96', 'desc': 'Disposition'},
            'SC': {'name': 'GREEN', 'rgb': '150, 255, 150', 'hex': '#96FF96', 'desc': 'Subject Characteristics'},
            'VS': {'name': 'ORANGE', 'rgb': '255, 190, 155', 'hex': '#FFBE9B', 'desc': 'Vital Signs'},
            'AE': {'name': 'RED', 'rgb': '255, 150, 150', 'hex': '#FF9696', 'desc': 'Adverse Events'},
            'CM': {'name': 'PURPLE', 'rgb': '200, 150, 255', 'hex': '#C896FF', 'desc': 'Concomitant Medications'},
            'EX': {'name': 'CYAN', 'rgb': '150, 255, 255', 'hex': '#96FFFF', 'desc': 'Exposure'},
            'LB': {'name': 'PINK', 'rgb': '255, 200, 200', 'hex': '#FFC8C8', 'desc': 'Laboratory Data'}
        }
    
    def process_excel_file(self, excel_path):
        """处理Excel文件"""
        try:
            df = pd.read_excel(excel_path)
            print(f"成功读取Excel文件: {excel_path}")
            print(f"数据行数: {len(df)}")
            print(f"列名: {list(df.columns)}")
            return df
        except Exception as e:
            print(f"读取Excel文件失败: {e}")
            return None
    
    def create_annotation_data(self, df):
        """创建注释数据"""
        if df is None or df.empty:
            return None
        
        # 按Domain分组
        if 'DOMAIN' in df.columns:
            domain_groups = df.groupby('DOMAIN')
            domain_list = list(domain_groups.groups.keys())
        else:
            domain_groups = [('Unknown', df)]
            domain_list = ['Unknown']

        annotation_data = {
            'summary': {
                'total_domains': len(domain_list),
                'total_variables': len(df),
                'domains': domain_list
            },
            'domains': {}
        }
        
        for domain, group in domain_groups:
            obsclass = group['OBSCLASS'].iloc[0] if 'OBSCLASS' in group.columns and not group.empty else 'Unknown'
            color_info = self.domain_colors.get(domain, {
                'name': 'GRAY', 'rgb': '200, 200, 200', 'hex': '#C8C8C8', 'desc': 'Unknown'
            })
            
            domain_data = {
                'domain': domain,
                'obsclass': obsclass,
                'domain_label': f"{domain} ({obsclass})",
                'color_info': color_info,
                'variables': []
            }
            
            for _, row in group.iterrows():
                var_info = {
                    'varnam': row.get('VARNAM', 'N/A'),
                    'varlabel': row.get('VARLABEL', 'N/A'),
                    'order': row.get('ORDER', 0),
                    'crf_page': row.get('CRF_P_NO', 'Unknown'),
                    'deftype': row.get('DEFTYPE', 'Unknown'),
                    'length': row.get('LENGTH', 'Unknown'),
                    'origin': row.get('ORIGIN', 'Unknown')
                }
                domain_data['variables'].append(var_info)
            
            annotation_data['domains'][domain] = domain_data
        
        return annotation_data
    
    def generate_html_preview(self, annotation_data, output_file):
        """生成HTML预览"""
        html_content = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>eCRF注释完整预览</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .header { background-color: #f0f0f0; padding: 20px; border-radius: 5px; margin-bottom: 20px; }
        .domain-section { margin: 30px 0; border: 1px solid #ddd; padding: 20px; border-radius: 5px; }
        .domain-box { 
            border: 2px solid black; 
            padding: 15px; 
            margin: 10px 0; 
            display: inline-block;
            font-weight: bold;
            font-size: 16px;
            border-radius: 3px;
        }
        .variable-container { margin: 15px 0; }
        .variable-box { 
            border: 1px solid black; 
            padding: 8px 12px; 
            margin: 5px; 
            display: inline-block;
            font-size: 12px;
            border-radius: 3px;
        }
        .color-legend { 
            display: flex; 
            flex-wrap: wrap; 
            gap: 15px; 
            margin: 20px 0; 
            padding: 15px;
            background-color: #f9f9f9;
            border-radius: 5px;
        }
        .color-item { 
            display: flex; 
            align-items: center; 
            gap: 8px; 
        }
        .color-sample { 
            width: 30px; 
            height: 20px; 
            border: 1px solid black; 
            border-radius: 3px;
        }
        table { border-collapse: collapse; width: 100%; margin: 15px 0; }
        th, td { border: 1px solid #ddd; padding: 10px; text-align: left; }
        th { background-color: #f2f2f2; font-weight: bold; }
        .summary { background-color: #e8f4fd; padding: 15px; border-radius: 5px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="header">
        <h1>eCRF注释完整预览</h1>
        <p>基于SDTM规范的eCRF注释指南</p>
    </div>
"""
        
        # 添加摘要信息
        summary = annotation_data['summary']
        html_content += f"""
    <div class="summary">
        <h2>摘要信息</h2>
        <p><strong>总Domain数:</strong> {summary['total_domains']}</p>
        <p><strong>总变量数:</strong> {summary['total_variables']}</p>
        <p><strong>包含的Domains:</strong> {', '.join(summary['domains'])}</p>
    </div>
"""
        
        # 添加颜色图例
        html_content += """
    <div class="color-legend">
        <h3 style="width: 100%; margin: 0 0 10px 0;">Domain颜色图例</h3>
"""
        
        for domain, color_info in self.domain_colors.items():
            html_content += f"""
        <div class="color-item">
            <div class="color-sample" style="background-color: {color_info['hex']};"></div>
            <span><strong>{domain}</strong> - {color_info['desc']}</span>
        </div>
"""
        
        html_content += """
    </div>
"""
        
        # 为每个Domain创建预览
        for domain, domain_data in annotation_data['domains'].items():
            color_hex = domain_data['color_info']['hex']
            html_content += f"""
    <div class="domain-section">
        <h2>Domain: {domain}</h2>
        
        <h3>Domain标识框预览</h3>
        <div class="domain-box" style="background-color: {color_hex};">
            {domain_data['domain_label']}
        </div>
        <p><strong>颜色:</strong> {domain_data['color_info']['name']} (RGB: {domain_data['color_info']['rgb']})</p>
        
        <h3>变量注释框预览</h3>
        <div class="variable-container">
"""
            
            for var in domain_data['variables']:
                html_content += f'            <div class="variable-box" style="background-color: {color_hex};">{var["varnam"]}</div>\n'
            
            html_content += f"""
        </div>
        
        <h3>变量详细信息</h3>
        <table>
            <tr>
                <th>变量名</th>
                <th>变量标签</th>
                <th>顺序</th>
                <th>CRF页码</th>
                <th>定义类型</th>
                <th>长度</th>
                <th>来源</th>
            </tr>
"""
            
            for var in domain_data['variables']:
                html_content += f"""
            <tr>
                <td><strong>{var['varnam']}</strong></td>
                <td>{var['varlabel']}</td>
                <td>{var['order']}</td>
                <td>{var['crf_page']}</td>
                <td>{var['deftype']}</td>
                <td>{var['length']}</td>
                <td>{var['origin']}</td>
            </tr>
"""
            
            html_content += """
        </table>
    </div>
"""
        
        html_content += """
    
    <div class="summary">
        <h2>注释实施说明</h2>
        <ol>
            <li><strong>Domain标识:</strong> 在每个表单的顶部添加相应的Domain标识框，使用对应的颜色背景</li>
            <li><strong>变量注释:</strong> 在每个数据字段旁边添加对应的VARNAM注释框</li>
            <li><strong>颜色一致性:</strong> 同一Domain的所有注释使用相同的背景颜色</li>
            <li><strong>字体要求:</strong> 注释文字使用黑色，确保清晰可读</li>
            <li><strong>边框样式:</strong> Domain框使用2px黑色边框，变量框使用1px黑色边框</li>
        </ol>
    </div>
    
</body>
</html>
"""
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(html_content)
        print(f"HTML预览已保存: {output_file}")
    
    def save_all_outputs(self, annotation_data, base_name="complete_ecrf_annotation"):
        """保存所有输出文件"""
        outputs = {}
        
        # JSON文件
        json_file = f"{base_name}.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(annotation_data, f, ensure_ascii=False, indent=2)
        outputs['json'] = json_file
        
        # CSV文件
        csv_file = f"{base_name}.csv"
        self._create_csv_output(annotation_data, csv_file)
        outputs['csv'] = csv_file
        
        # HTML预览
        html_file = f"{base_name}.html"
        self.generate_html_preview(annotation_data, html_file)
        outputs['html'] = html_file
        
        # 文本报告
        txt_file = f"{base_name}_report.txt"
        self._create_text_report(annotation_data, txt_file)
        outputs['report'] = txt_file
        
        return outputs
    
    def _create_csv_output(self, annotation_data, output_file):
        """创建CSV输出"""
        rows = []
        
        for domain, domain_data in annotation_data['domains'].items():
            # Domain行
            rows.append({
                'Domain': domain,
                'Type': 'Domain',
                'Position': 'Form Header',
                'Text': domain_data['domain_label'],
                'Color': domain_data['color_info']['name'],
                'RGB': domain_data['color_info']['rgb'],
                'Hex': domain_data['color_info']['hex'],
                'Variable': '',
                'Label': domain_data['obsclass'],
                'Order': 0
            })
            
            # 变量行
            for var in domain_data['variables']:
                rows.append({
                    'Domain': domain,
                    'Type': 'Variable',
                    'Position': f"Field for {var['varlabel']}",
                    'Text': var['varnam'],
                    'Color': domain_data['color_info']['name'],
                    'RGB': domain_data['color_info']['rgb'],
                    'Hex': domain_data['color_info']['hex'],
                    'Variable': var['varnam'],
                    'Label': var['varlabel'],
                    'Order': var['order']
                })
        
        df = pd.DataFrame(rows)
        df.to_csv(output_file, index=False, encoding='utf-8-sig')
        print(f"CSV文件已保存: {output_file}")
    
    def _create_text_report(self, annotation_data, output_file):
        """创建文本报告"""
        report_lines = []
        report_lines.append("="*80)
        report_lines.append("eCRF注释完整报告")
        report_lines.append("="*80)
        
        summary = annotation_data['summary']
        report_lines.append(f"\n摘要信息:")
        report_lines.append(f"- 总Domain数: {summary['total_domains']}")
        report_lines.append(f"- 总变量数: {summary['total_variables']}")
        report_lines.append(f"- 包含的Domains: {', '.join(summary['domains'])}")
        
        for domain, domain_data in annotation_data['domains'].items():
            report_lines.append(f"\n{'-'*60}")
            report_lines.append(f"Domain: {domain}")
            report_lines.append(f"{'-'*60}")
            report_lines.append(f"OBSCLASS: {domain_data['obsclass']}")
            report_lines.append(f"Domain标签: {domain_data['domain_label']}")
            report_lines.append(f"颜色: {domain_data['color_info']['name']} ({domain_data['color_info']['rgb']})")
            report_lines.append(f"变量数量: {len(domain_data['variables'])}")
            
            report_lines.append(f"\n变量列表:")
            for i, var in enumerate(domain_data['variables'], 1):
                report_lines.append(f"  {i:2d}. {var['varnam']:12s} -> {var['varlabel']}")
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(report_lines))
        print(f"文本报告已保存: {output_file}")

def main():
    """主函数"""
    excel_path = "dm_spec.xlsx"
    
    print("完整eCRF注释器")
    print("="*60)
    
    if not os.path.exists(excel_path):
        print(f"错误: Excel文件不存在 - {excel_path}")
        return
    
    annotator = CompleteeCRFAnnotator()
    
    # 处理Excel文件
    df = annotator.process_excel_file(excel_path)
    if df is None:
        return
    
    # 创建注释数据
    annotation_data = annotator.create_annotation_data(df)
    if annotation_data is None:
        print("创建注释数据失败")
        return
    
    # 保存所有输出
    outputs = annotator.save_all_outputs(annotation_data)
    
    print("\n" + "="*60)
    print("处理完成! 生成的文件:")
    for file_type, filename in outputs.items():
        print(f"- {file_type.upper()}: {filename}")
    
    print(f"\n建议:")
    print(f"1. 打开 {outputs['html']} 查看完整的注释预览")
    print(f"2. 使用 {outputs['csv']} 作为注释实施的参考表")
    print(f"3. 参考 {outputs['report']} 了解详细的注释说明")

if __name__ == "__main__":
    main()
