import pandas as pd
import os
import glob
from pathlib import Path

def merge_excel_files_simple(directory_path):
    """简化版本的Excel文件合并程序"""
    
    # 获取所有xlsx文件
    xlsx_files = glob.glob(os.path.join(directory_path, "*.xlsx"))
    
    if not xlsx_files:
        print("没有找到xlsx文件")
        return
    
    print(f"找到 {len(xlsx_files)} 个xlsx文件")
    
    all_data = []
    
    for file_path in xlsx_files:
        file_name = Path(file_path).stem
        print(f"处理: {file_name}")
        
        try:
            # 读取第一个sheet（假设所有文件的sheet名字都一样）
            df = pd.read_excel(file_path)
            
            # 添加文件名列
            df.insert(0, 'excel_file', file_name)
            
            # 如果DataFrame为空，创建一个状态行
            if df.empty or len(df) <= 1:
                status_data = {
                    'excel_file': file_name,
                    'status': '✓ Congratulations! 日志通过检查!',
                    'line': '',
                    'level': '', 
                    'rx': ''
                }
                all_data.append(pd.DataFrame([status_data]))
            else:
                all_data.append(df)
                
        except Exception as e:
            print(f"处理 {file_name} 时出错: {e}")
            # 添加错误信息
            error_data = {
                'excel_file': file_name,
                'status': f'读取错误: {str(e)}',
                'line': '',
                'level': '',
                'rx': ''
            }
            all_data.append(pd.DataFrame([error_data]))
    
    # 合并所有数据
    if all_data:
        merged_df = pd.concat(all_data, ignore_index=True, sort=False)
        
        # 保存结果
        output_file = "merged_results.xlsx"
        merged_df.to_excel(output_file, index=False)
        
        print(f"\n合并完成！共 {len(merged_df)} 行数据")
        print(f"结果保存到: {output_file}")
        print("\n前5行预览:")
        print(merged_df.head())
    else:
        print("没有数据可合并")

# 使用示例
if __name__ == "__main__":
    # 您可以修改这个路径
    directory = r"E:\SAS\sas_check\20250619\彭星-LY-M001-GD-101-m5\LY-M001-GD-101-m5\datasets\LY-M001-GD-101\analysis\legacy\programs\adam"
    
    merge_excel_files_simple(directory)
