#!/usr/bin/env python3
"""
完美版PDF注释脚本
解决颜色框覆盖问题 - 让文字在颜色框里而不是覆盖原内容
"""

import fitz  # PyMuPDF
import os

def annotate_pdf_perfectly():
    """完美版注释 - 文字在颜色框里，不遮挡原内容"""
    
    pdf_path = "HY1005-2023-2-P1_Unique eCRF_V3.0.pdf"
    output_path = "HY1005-2023-2-P1_Unique eCRF_V3.0_PERFECT_annotated.pdf"
    
    # DM域颜色
    domain_color = (0.75, 1, 1)  # RGB(191,255,255)
    
    # 严格按照Excel数据的字段映射
    field_mappings = {
        "出生日期": "BRTHDTC",
        "年龄": "AGE",
        "周岁": "AGEU",
        "性别": "SEX"
    }
    
    try:
        doc = fitz.open(pdf_path)
        print(f"开始完美注释...")
        print(f"解决方案: 使用自由文本注释，文字自带彩色背景")
        
        domain_added = False
        
        # 遍历所有页面
        for page_num in range(len(doc)):
            page = doc[page_num]
            print(f"\n处理第 {page_num + 1} 页...")
            
            # 1. 添加Domain标识 - 使用自由文本注释
            if not domain_added:
                if page_num >= 6:  # 从第7页开始查找表单
                    text_instances = page.search_for("人口学资料")
                    for rect in text_instances:
                        # 检查是否在表单位置
                        if rect.y0 < page.rect.height * 0.3:
                            print(f"找到表单标题 '人口学资料' at {rect}")
                            
                            # 使用自由文本注释 - 文字自带背景
                            domain_rect = fitz.Rect(
                                rect.x0 - 130,
                                rect.y0 - 5,
                                rect.x0 - 10,
                                rect.y0 + 15
                            )
                            
                            # 创建自由文本注释
                            text_annot = page.add_freetext_annot(
                                domain_rect,
                                "DM(人口学资料)",
                                fontsize=10,
                                fontname="helv",
                                text_color=(0, 0, 0),        # 黑色文字
                                fill_color=domain_color,      # 彩色背景
                                border_color=(0, 0, 0)       # 黑色边框
                            )
                            text_annot.set_border(width=2)
                            text_annot.update()
                            
                            print(f"已添加Domain标识: DM(人口学资料) at {domain_rect}")
                            domain_added = True
                            break
            
            # 2. 添加变量注释 - 使用自由文本注释
            for pdf_field_name, varnam in field_mappings.items():
                text_instances = page.search_for(pdf_field_name)
                if text_instances:
                    rect = text_instances[0]
                    print(f"找到字段 '{pdf_field_name}' at {rect}")
                    
                    # 计算注释位置 - 避免遮挡原文字
                    if pdf_field_name == "周岁":
                        # 年龄单位在"周岁"右侧
                        var_rect = fitz.Rect(
                            rect.x1 + 5,
                            rect.y0 - 2,
                            rect.x1 + 50,
                            rect.y0 + 15
                        )
                    else:
                        # 其他字段在右侧
                        var_rect = fitz.Rect(
                            rect.x1 + 10,
                            rect.y0 - 2,
                            rect.x1 + 70,
                            rect.y0 + 16
                        )
                    
                    # 检查是否超出页面边界
                    if var_rect.x1 > page.rect.width - 20:
                        # 调整到左侧
                        var_rect = fitz.Rect(
                            rect.x0 - 70,
                            rect.y0 - 2,
                            rect.x0 - 10,
                            rect.y0 + 16
                        )
                    
                    # 创建自由文本注释 - 文字自带彩色背景
                    text_annot = page.add_freetext_annot(
                        var_rect,
                        varnam,
                        fontsize=9,
                        fontname="helv",
                        text_color=(0, 0, 0),        # 黑色文字
                        fill_color=domain_color,      # 彩色背景
                        border_color=(0, 0, 0)       # 黑色边框
                    )
                    text_annot.set_border(width=1)
                    text_annot.update()
                    
                    print(f"已添加变量注释: {varnam} for '{pdf_field_name}' at {var_rect}")
        
        # 保存完美版本
        doc.save(output_path)
        doc.close()
        
        print(f"\n完美注释完成!")
        print(f"输出文件: {output_path}")
        
        return True
        
    except Exception as e:
        print(f"注释失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_annotation_methods():
    """测试不同的注释方法"""
    print("测试注释方法:")
    print("方法1: add_rect_annot + insert_text (当前方法)")
    print("  问题: 矩形和文字是分离的，可能遮挡原内容")
    print()
    print("方法2: add_freetext_annot (新方法)")
    print("  优势: 文字自带背景色，作为一个整体，不遮挡原内容")
    print("  特点: 文字在颜色框里，而不是颜色框覆盖文字")

if __name__ == "__main__":
    print("完美版PDF注释脚本")
    print("="*60)
    
    # 测试注释方法
    test_annotation_methods()
    
    # 检查PDF文件
    pdf_path = "HY1005-2023-2-P1_Unique eCRF_V3.0.pdf"
    if not os.path.exists(pdf_path):
        print(f"错误: PDF文件不存在 - {pdf_path}")
        exit(1)
    
    print("\n执行完美注释...")
    success = annotate_pdf_perfectly()
    
    if success:
        print("\n完美解决方案!")
        print("关键改进:")
        print("1. 使用 add_freetext_annot 替代 add_rect_annot + insert_text")
        print("2. 文字自带彩色背景，作为一个注释整体")
        print("3. 不会遮挡PDF原有内容")
        print("4. 文字在颜色框里，而不是颜色框覆盖文字")
        print("\n最终完美文件: HY1005-2023-2-P1_Unique eCRF_V3.0_PERFECT_annotated.pdf")
    else:
        print("\n注释失败，请检查错误信息")
