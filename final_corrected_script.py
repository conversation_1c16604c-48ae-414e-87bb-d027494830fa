#!/usr/bin/env python3
"""
最终修正版PDF注释脚本
解决表名称位置、年龄单位标注、字体颜色问题
"""

import fitz  # PyMuPDF
import os

def analyze_pdf_structure():
    """详细分析PDF结构，找到正确的表名称位置"""
    pdf_path = "HY1005-2023-2-P1_Unique eCRF_V3.0.pdf"
    
    try:
        doc = fitz.open(pdf_path)
        print("详细分析PDF结构...")
        
        # 重点分析第7页（根据之前的结果，这里有完整的表单）
        page = doc[6]  # 第7页，索引为6
        print(f"\n=== 第7页详细分析 ===")
        
        # 获取页面所有文本
        text_dict = page.get_text("dict")
        
        # 查找所有文本块
        for block in text_dict["blocks"]:
            if "lines" in block:
                for line in block["lines"]:
                    for span in line["spans"]:
                        text = span["text"].strip()
                        if text and len(text) > 1:
                            bbox = span["bbox"]
                            print(f"文本: '{text}' at {bbox}")
        
        doc.close()
        
    except Exception as e:
        print(f"分析失败: {e}")

def annotate_pdf_final():
    """最终修正版注释"""
    
    pdf_path = "HY1005-2023-2-P1_Unique eCRF_V3.0.pdf"
    output_path = "HY1005-2023-2-P1_Unique eCRF_V3.0_FINAL_annotated.pdf"
    
    # DM域颜色 - 确保足够明显
    domain_color = (0.75, 1, 1)  # RGB(191,255,255)
    
    # 建立完整的字段映射
    field_mappings = {
        "出生日期": "BRTHDTC",
        "年龄": "AGE", 
        "周岁": "AGEU",        # 年龄单位标注到"周岁"
        "性别": "SEX",
        # 可能还有其他字段
        "民族": "RACE",
        "身高": "HEIGHT", 
        "体重": "WEIGHT",
        "BMI": "BMI"
    }
    
    try:
        doc = fitz.open(pdf_path)
        print(f"开始最终修正注释...")
        print(f"PDF总页数: {len(doc)}")
        
        domain_added = False
        
        # 遍历所有页面
        for page_num in range(len(doc)):
            page = doc[page_num]
            print(f"\n处理第 {page_num + 1} 页...")
            
            # 1. 查找表单内的表名称（不是目录中的）
            if not domain_added:
                # 在表单页面查找表头
                if page_num >= 6:  # 从第7页开始查找表单
                    # 查找表单顶部的"人口学资料"标题
                    text_instances = page.search_for("人口学资料")
                    for rect in text_instances:
                        # 检查是否在页面上半部分（表单标题位置）
                        if rect.y0 < page.rect.height * 0.3:  # 页面上30%区域
                            print(f"找到表单标题 '人口学资料' at {rect}")
                            
                            # 在表标题上方添加Domain标识
                            domain_rect = fitz.Rect(
                                rect.x0,
                                rect.y0 - 25,
                                rect.x0 + 120,
                                rect.y0 - 5
                            )
                            
                            # 添加彩色背景
                            annot = page.add_rect_annot(domain_rect)
                            annot.set_colors(stroke=(0, 0, 0), fill=domain_color)
                            annot.set_border(width=2)
                            annot.set_opacity(1.0)  # 完全不透明
                            annot.update()
                            
                            # 添加黑色文字在彩色背景上
                            text_point = fitz.Point(domain_rect.x0 + 8, domain_rect.y0 + 15)
                            page.insert_text(text_point, "DM(人口学资料)", 
                                           fontsize=11, color=(0, 0, 0), fontname="helv")
                            
                            print(f"已添加Domain标识: DM(人口学资料) at {domain_rect}")
                            domain_added = True
                            break
            
            # 2. 查找并标注所有字段
            for pdf_field_name, varnam in field_mappings.items():
                text_instances = page.search_for(pdf_field_name)
                if text_instances:
                    rect = text_instances[0]
                    print(f"找到字段 '{pdf_field_name}' at {rect}")
                    
                    # 计算注释框位置
                    if pdf_field_name == "周岁":
                        # 年龄单位特殊处理 - 可能在"周岁"右侧或上方
                        var_rect = fitz.Rect(
                            rect.x1 + 5,
                            rect.y0 - 2,
                            rect.x1 + 60,
                            rect.y0 + 15
                        )
                    else:
                        # 其他字段在右侧
                        var_rect = fitz.Rect(
                            rect.x1 + 10,
                            rect.y0 - 2,
                            rect.x1 + 80,
                            rect.y0 + 16
                        )
                    
                    # 添加彩色背景
                    annot = page.add_rect_annot(var_rect)
                    annot.set_colors(stroke=(0, 0, 0), fill=domain_color)
                    annot.set_border(width=1)
                    annot.set_opacity(1.0)  # 完全不透明
                    annot.update()
                    
                    # 添加黑色文字在彩色背景上
                    text_point = fitz.Point(var_rect.x0 + 3, var_rect.y0 + 11)
                    page.insert_text(text_point, varnam, 
                                   fontsize=9, color=(0, 0, 0), fontname="helv")
                    
                    print(f"已添加变量注释: {varnam} for '{pdf_field_name}' at {var_rect}")
        
        # 保存最终版本
        doc.save(output_path)
        doc.close()
        
        print(f"\n最终修正完成!")
        print(f"输出文件: {output_path}")
        
        return True
        
    except Exception as e:
        print(f"注释失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def search_specific_terms():
    """搜索特定术语的位置"""
    pdf_path = "HY1005-2023-2-P1_Unique eCRF_V3.0.pdf"
    
    try:
        doc = fitz.open(pdf_path)
        print("搜索关键术语位置...")
        
        search_terms = ["人口学资料", "周岁", "出生日期", "年龄", "性别"]
        
        for page_num in range(min(15, len(doc))):  # 检查前15页
            page = doc[page_num]
            
            for term in search_terms:
                text_instances = page.search_for(term)
                if text_instances:
                    print(f"第{page_num+1}页找到 '{term}':")
                    for i, rect in enumerate(text_instances):
                        print(f"  位置{i+1}: {rect} (y坐标: {rect.y0:.1f})")
        
        doc.close()
        
    except Exception as e:
        print(f"搜索失败: {e}")

if __name__ == "__main__":
    print("最终修正版PDF注释脚本")
    print("="*60)
    
    # 检查PDF文件
    pdf_path = "HY1005-2023-2-P1_Unique eCRF_V3.0.pdf"
    if not os.path.exists(pdf_path):
        print(f"错误: PDF文件不存在 - {pdf_path}")
        exit(1)
    
    print("\n1. 搜索关键术语位置...")
    search_specific_terms()
    
    print("\n2. 执行最终修正注释...")
    success = annotate_pdf_final()
    
    if success:
        print("\n🎉 最终修正完成!")
        print("主要修正:")
        print("1. ✅ 查找表单内的表名称位置（不是目录）")
        print("2. ✅ 在'周岁'位置标注AGEU")
        print("3. ✅ 使用彩色背景+黑色字体，确保清晰可见")
        print("4. ✅ 设置完全不透明，避免字体颜色过浅")
        print("\n📁 最终文件: HY1005-2023-2-P1_Unique eCRF_V3.0_FINAL_annotated.pdf")
    else:
        print("\n❌ 注释失败，请检查错误信息")
