
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>eCRF注释预览</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .domain-box { 
            background-color: #BFFFFF; 
            border: 2px solid black; 
            padding: 10px; 
            margin: 10px 0; 
            display: inline-block;
            font-weight: bold;
        }
        .variable-box { 
            background-color: #BFFFFF; 
            border: 1px solid black; 
            padding: 5px; 
            margin: 5px; 
            display: inline-block;
            font-size: 12px;
        }
        .section { margin: 20px 0; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <h1>eCRF注释预览</h1>
    
    <div class="section">
        <h2>Domain标识框预览</h2>
        <div class="domain-box">DM (人口学资料)</div>
        <p>颜色: BLUE (RGB: 191, 255, 255)</p>
    </div>
    
    <div class="section">
        <h2>变量注释框预览</h2>
        <div class="variable-box">BRTHDTC</div>
        <div class="variable-box">AGE</div>
        <div class="variable-box">AGEU</div>
        <div class="variable-box">SEX</div>

    </div>
    
    <div class="section">
        <h2>注释详细信息</h2>
        <table>
            <tr><th>类型</th><th>位置</th><th>注释文本</th><th>说明</th></tr>
            <tr>
                <td>Domain</td>
                <td>表单头部</td>
                <td>DM (人口学资料)</td>
                <td>Domain标识</td>
            </tr>

            <tr>
                <td>Variable</td>
                <td>出生日期/时间字段</td>
                <td>BRTHDTC</td>
                <td>出生日期/时间</td>
            </tr>

            <tr>
                <td>Variable</td>
                <td>年龄字段</td>
                <td>AGE</td>
                <td>年龄</td>
            </tr>

            <tr>
                <td>Variable</td>
                <td>年龄单位字段</td>
                <td>AGEU</td>
                <td>年龄单位</td>
            </tr>

            <tr>
                <td>Variable</td>
                <td>性别字段</td>
                <td>SEX</td>
                <td>性别</td>
            </tr>

        </table>
    </div>
</body>
</html>
